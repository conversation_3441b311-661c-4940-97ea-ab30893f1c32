/**
 * 游戏状态管理器类
 * 负责管理游戏中的状态数据，包括角色、物品、装备等
 */

// 引入模型类
import Character from '../models/Character';
import Equipment from '../models/Equipment';
import Item from '../models/Item';
import EventEmitter from '../utils/EventEmitter.js';
import SwordHeartManager from './SwordHeartManager.js';
import ArenaManager from './ArenaManager.js';
import SwordBoneManager from './SwordBoneManager.js';
import game from '../../game.js';

class GameStateManager extends EventEmitter {
  constructor() {
    // 调用父类构造函数初始化事件系统
    super();

    // 游戏数据
    this.state = {
      // 玩家信息
      player: {
        nickname: '修仙者',
        avatarUrl: '',
        dongfuLevel: 1,
        resources: {
          xianyu: 1000,  // 仙玉
          lingshi: 1000,  // 灵石
          swordIntent: 0,  // 剑意，用于剑心系统
          swordYuan: 10   // 剑心缘，用于剑心抽卡
        },
        // VIP相关数据
        vipLevel: 0,
        totalRecharge: 0,
        lastVIPRewardTime: null,
        // 添加玩家布阵数据
        formation: []
      },

      // 角色列表
      characters: [],

      // 物品列表
      items: [],

      // 装备列表
      equipments: [],

      // 怪物列表
      monsters: [],

      // 抽卡保底计数器
      gachaPity: 0,

      // 剑心数据
      swordHearts: null,

      // 服务器信息
      server: {
        id: 1,
        name: '太虚境'
      }
    };

    // 初始化剑心管理器
    this.swordHeartManager = new SwordHeartManager();

    // 初始化竞技场管理器
    this.arenaManager = new ArenaManager();

    // 初始化剑骨管理器
    this.swordBoneManager = new SwordBoneManager();

    // 添加公共访问的gameState属性
    this.gameState = this.state;

    // 初始化游戏数据
    this.initGameState();
  }

  // 初始化游戏数据
  initGameState() {
    // 创建初始角色
    this.createInitialCharacter();

    // 创建初始装备
    this.createInitialEquipments();

    // 创建初始物品
    this.createInitialItems();

    // 创建初始怪物
    this.createInitialMonsters();

    // 确保gameState引用与state相同
    this.gameState = this.state;
  }

  // 创建初始角色
  createInitialCharacter() {
    const initialCharacter = new Character({
      id: 1,
      name: '女剑仙',
      level: 1,
      exp: 0,
      attributes: {
        hp: 120,
        attack: 12,
        defense: 6,
        speed: 11,
        critRate: 0.06,
        critDamage: 1.6
      },
      skills: [
        { id: 1, name: '剑气', level: 1, damage: 20, cooldown: 2 }
      ],
      cultivation: '练气期一层' // 使用新的境界命名规范
    });

    // 计算角色战力
    initialCharacter.calculatePower();

    // 添加到角色列表
    this.state.characters.push(initialCharacter);
  }

  // 创建初始装备
  createInitialEquipments() {
    const equipments = [
      new Equipment({
        id: 1,
        name: '新手剑',
        type: 'weapon',
        level: 1,
        quality: 'common',
        attributes: {
          attack: 5
        },
        description: '一把普通的剑，适合新手使用。'
      }),
      new Equipment({
        id: 2,
        name: '布衣',
        type: 'armor',
        level: 1,
        quality: 'common',
        attributes: {
          defense: 3
        },
        description: '普通的布制衣物，提供基础防护。'
      }),
      new Equipment({
        id: 3,
        name: '玉佩',
        type: 'accessory',
        level: 1,
        quality: 'common',
        attributes: {
          hp: 10,
          speed: 1
        },
        description: '普通的玉佩，能提供些许生命力和灵活性。'
      }),
      new Equipment({
        id: 4,
        name: '灵气珠',
        type: 'artifact',
        level: 1,
        quality: 'common',
        attributes: {
          attack: 2,
          defense: 2
        },
        description: '蕴含少量灵气的宝珠，能提升修炼者的攻防能力。'
      })
    ];

    // 添加到装备列表
    this.state.equipments = equipments;
  }

  // 创建初始物品
  createInitialItems() {
    const items = [
      new Item({
        id: 1,
        name: '灵药',
        type: 'consumable',
        quality: 'common',
        count: 5,
        effect: {
          type: 'heal',
          value: 50
        },
        description: '恢复少量生命值的灵药。'
      }),
      new Item({
        id: 2,
        name: '灵石原矿',
        type: 'material',
        quality: 'common',
        count: 10,
        description: '未经提炼的灵石原矿，可用于提炼灵石。'
      }),
      new Item({
        id: 'linglidan_small',
        name: '小灵力丹',
        type: 'consumable',
        quality: 'common',
        count: 5,
        effects: {
          lingli: 100
        },
        description: '服用后增加少量灵力，加速修炼进度。'
      }),
      new Item({
        id: 'linglidan_medium',
        name: '中灵力丹',
        type: 'consumable',
        quality: 'uncommon',
        count: 3,
        effects: {
          lingli: 500
        },
        description: '服用后增加中量灵力，加速修炼进度。'
      }),
      new Item({
        id: 'linglidan_large',
        name: '大灵力丹',
        type: 'consumable',
        quality: 'rare',
        count: 1,
        effects: {
          lingli: 2000
        },
        description: '服用后增加大量灵力，加速修炼进度。'
      }),
      new Item({
        id: 'breakthrough_normal',
        name: '普通突破丹',
        type: 'consumable',
        quality: 'common',
        count: 3,
        effects: {
          breakthrough: 0.1
        },
        description: '突破时使用，增加10%突破成功率'
      }),
      new Item({
        id: 'breakthrough_good',
        name: '上品突破丹',
        type: 'consumable',
        quality: 'uncommon',
        count: 2,
        effects: {
          breakthrough: 0.2
        },
        description: '突破时使用，增加20%突破成功率'
      }),
      new Item({
        id: 'breakthrough_rare',
        name: '稀有突破丹',
        type: 'consumable',
        quality: 'rare',
        count: 1,
        effects: {
          breakthrough: 0.3
        },
        description: '突破时使用，增加30%突破成功率'
      }),
      // 境界特定突破丹
      new Item({
        id: 'breakthrough_lianqi',
        name: '练气突破丹',
        type: 'consumable',
        quality: 'uncommon',
        count: 2,
        effects: {
          breakthrough: 0.25,
          cultivation: '练气期'
        },
        description: '练气期突破时使用，增加25%突破成功率'
      }),
      new Item({
        id: 'breakthrough_zhuji',
        name: '筑基突破丹',
        type: 'consumable',
        quality: 'rare',
        count: 1,
        effects: {
          breakthrough: 0.3,
          cultivation: '筑基期'
        },
        description: '筑基期突破时使用，增加30%突破成功率'
      }),
      // 添加兽材
      new Item({
        id: 'beast_material_1',
        name: '一阶兽材',
        type: 'material',
        quality: 1,
        count: 20,
        tier: 1,
        description: '初级锻造材料，可用于锻造一阶装备'
      }),
      new Item({
        id: 'beast_material_2',
        name: '二阶兽材',
        type: 'material',
        quality: 2,
        count: 5,
        tier: 2,
        description: '中级锻造材料，可用于锻造二阶装备，需要筑基期以上境界才能使用'
      })
    ];

    // 添加到物品列表
    this.state.items = items;
  }

  // 创建初始怪物
  createInitialMonsters() {
    const monsters = [
      // 普通小怪
      new Character({
        id: 101,
        name: '野狼',
        level: 1,
        attributes: {
          hp: 80,
          attack: 8,
          defense: 3,
          speed: 12,
          critRate: 0.03,
          critDamage: 1.3
        },
        cultivation: '凡兽'
      }),
      new Character({
        id: 102,
        name: '山贼',
        level: 2,
        attributes: {
          hp: 100,
          attack: 10,
          defense: 4,
          speed: 9,
          critRate: 0.04,
          critDamage: 1.4
        },
        cultivation: '凡人'
      }),
      new Character({
        id: 103,
        name: '妖狐',
        level: 3,
        attributes: {
          hp: 120,
          attack: 12,
          defense: 5,
          speed: 14,
          critRate: 0.05,
          critDamage: 1.5,
          daoRule: 1
        },
        cultivation: '炼气期'
      }),
      new Character({
        id: 104,
        name: '蓝鳞蛇',
        level: 4,
        attributes: {
          hp: 150,
          attack: 15,
          defense: 7,
          speed: 10,
          critRate: 0.06,
          critDamage: 1.6,
          penetration: 2,
          damageBonus: 0.05
        },
        cultivation: '炼气期'
      }),
      new Character({
        id: 105,
        name: '修炼者',
        level: 5,
        attributes: {
          hp: 200,
          attack: 18,
          defense: 9,
          speed: 11,
          critRate: 0.07,
          critDamage: 1.7,
          daoRule: 2,
          skillDamageBonus: 0.1
        },
        skills: [
          { id: 1, name: '灵气弹', level: 1, damage: 25, cooldown: 3 }
        ],
        cultivation: '炼气期'
      })
    ];

    // 计算怪物战力
    monsters.forEach(monster => monster.calculatePower());

    // 添加到怪物列表
    this.state.monsters = monsters;
  }

  /**
   * 获取游戏状态数据
   * @returns {Object} 游戏状态数据
   */
  getGameState() {
    return this.state;
  }

  /**
   * 加载游戏状态数据
   * @param {Object} gameState 游戏状态数据
   */
  loadGameState(gameState) {
    if (!gameState) return;

    try {
      // 加载玩家数据
      if (gameState.player) {
        this.state.player = gameState.player;
      }

      // 加载角色数据
      if (gameState.characters && Array.isArray(gameState.characters)) {
        this.state.characters = gameState.characters.map(charData => {
          // 如果是Character实例，直接使用
          if (charData instanceof Character) return charData;

          // 如果角色有装备，确保装备对象正确实例化
          if (charData.equipment) {
            const equipSlots = ['weapon', 'armor', 'accessory', 'artifact'];
            for (const slot of equipSlots) {
              if (charData.equipment[slot] && !(charData.equipment[slot] instanceof Equipment)) {
                charData.equipment[slot] = new Equipment(charData.equipment[slot]);
              }
            }
          }

          // 否则创建新的Character实例
          return new Character(charData);
        });
      }

      // 加载物品数据
      if (gameState.items && Array.isArray(gameState.items)) {
        this.state.items = gameState.items.map(itemData => {
          // 如果是Item实例，直接使用
          if (itemData instanceof Item) return itemData;

          // 否则创建新的Item实例
          return new Item(itemData);
        });
      }

      // 加载装备数据
      if (gameState.equipments && Array.isArray(gameState.equipments)) {
        this.state.equipments = gameState.equipments.map(equipData => {
          // 如果是Equipment实例，直接使用
          if (equipData instanceof Equipment) return equipData;

          // 否则创建新的Equipment实例
          return new Equipment(equipData);
        });
      } else {
        // 如果没有装备数据，初始化为空数组
        this.state.equipments = [];
      }

      // 加载怪物数据
      if (gameState.monsters && Array.isArray(gameState.monsters)) {
        this.state.monsters = gameState.monsters.map(monsterData => {
          // 如果是Character实例，直接使用
          if (monsterData instanceof Character) return monsterData;

          // 否则创建新的Character实例
          return new Character(monsterData);
        });
      }

      // 加载剑心数据
      if (gameState.swordHearts) {
        this.state.swordHearts = gameState.swordHearts;
        this.swordHeartManager.fromJSON(gameState.swordHearts);
      }

      // 加载竞技场数据
      if (gameState.arena) {
        this.state.arena = gameState.arena;
        this.arenaManager.fromJSON(gameState.arena);
      }

      // 加载剑骨数据
      if (gameState.swordBone) {
        this.state.swordBone = gameState.swordBone;
        this.swordBoneManager.fromJSON(gameState.swordBone);
      }

      // 确保gameState引用与state相同
      this.gameState = this.state;

      // 触发数据加载完成事件
      this.emit('gameStateLoaded', this.state);

      console.log('游戏状态加载成功');
    } catch (error) {
      console.error('游戏状态加载失败:', error);
      throw error;
    }
  }

  // 获取玩家信息
  getPlayer() {
    return this.state.player;
  }

  // 更新玩家信息
  setPlayer(player) {
    this.state.player = { ...this.state.player, ...player };
    this.emit('playerDataChanged', this.state.player);
  }

  // 更新玩家资源
  updatePlayerResources(resources) {
    this.state.player.resources = { ...this.state.player.resources, ...resources };
    this.emit('playerDataChanged', this.state.player);
  }

  // 更新洞府等级
  updateDongfuLevel(level) {
    this.state.player.dongfuLevel = level;
    this.emit('playerDataChanged', this.state.player);
  }

  // 根据ID获取角色
  getCharacterById(id) {
    return this.state.characters.find(char => char.id === id);
  }

  // 获取所有角色
  getCharacters() {
    return this.state.characters;
  }

  // 添加角色
  addCharacter(character) {
    this.state.characters.push(character);
    this.emit('characterChanged', { action: 'add', character });
  }

  // 更新角色
  updateCharacter(id, updatedCharacter) {
    const index = this.state.characters.findIndex(char => char.id === id);
    if (index >= 0) {
      this.state.characters[index] = updatedCharacter;

      // 如果更新的是主角（ID为1的女剑仙），同步所有其他角色等级
      if (id === 1) {
        this.syncAllCharacterLevels(updatedCharacter.level, updatedCharacter.cultivation);
      }

      return true;
    }
    return false;
  }

  // 同步所有角色等级到主角等级
  syncAllCharacterLevels(mainLevel, mainCultivation) {
    this.state.characters.forEach(character => {
      if (character.id !== 1) { // 跳过主角自身
        character.level = mainLevel;
        character.cultivation = mainCultivation;
        // 更新角色属性以匹配新等级
        if (typeof character.updateAttributes === 'function') {
          character.updateAttributes();
        }
      }
    });
  }

  // 移除角色
  removeCharacter(id) {
    const index = this.state.characters.findIndex(char => char.id === id);
    if (index !== -1) {
      const character = this.state.characters[index];
      this.state.characters.splice(index, 1);
      this.emit('characterChanged', { action: 'remove', character });
    }
  }

  // 根据ID获取物品
  getItem(id) {
    return this.state.items.find(item => item.id === id);
  }

  // 获取所有物品
  getItems() {
    return this.state.items;
  }

  // 添加物品
  addItem(item) {
    if (!item) return false;

    // 获取最大ID
    const maxId = this.state.items.reduce((max, item) => Math.max(max, item.id), 0);

    // 设置新ID
    if (!item.id) {
      item.id = maxId + 1;
    }

    // 添加物品
    this.state.items.push(item);

    // 触发物品更新事件
    this.emit('inventoryChanged');

    return true;
  }

  // 更新物品
  updateItem(id, updatedItem) {
    if (!id || !updatedItem) return false;

    // 查找物品索引
    const index = this.state.items.findIndex(item => item.id === id);

    if (index === -1) return false;

    // 更新物品
    this.state.items[index] = updatedItem;

    // 触发物品更新事件
    this.emit('inventoryChanged');

    return true;
  }

  // 移除物品
  removeItem(id, count = 1) {
    const index = this.state.items.findIndex(item => item.id === id);
    if (index === -1) return false;

    const item = this.state.items[index];
    if (item.count > count) {
      // 减少数量
      item.count -= count;
    } else {
      // 移除物品
      this.state.items.splice(index, 1);
    }

    this.emit('inventoryChanged', { action: 'remove', item, count });
    return true;
  }

  // 根据ID获取装备
  getEquipment(id) {
    return this.state.equipments.find(equip => equip.id === id);
  }

  // 获取所有装备
  getEquipments() {
    return this.state.equipments;
  }

  // 添加装备
  addEquipment(equipment) {
    this.state.equipments.push(equipment);
    this.emit('inventoryChanged', { action: 'add', equipment });
  }

  // 移除装备
  removeEquipment(id) {
    const index = this.state.equipments.findIndex(equip => equip.id === id);
    if (index !== -1) {
      const equipment = this.state.equipments[index];
      this.state.equipments.splice(index, 1);
      this.emit('inventoryChanged', { action: 'remove', equipment });
    }
  }

  // 获取技能
  getSkills() {
    const skills = [];

    // 收集所有角色的技能
    this.state.characters.forEach(character => {
      if (character.skills && Array.isArray(character.skills)) {
        skills.push(...character.skills);
      }
    });

    return skills;
  }

  // 更新技能
  updateSkill(characterId, skillId, updatedSkill) {
    const character = this.getCharacterById(characterId);
    if (!character) return false;

    const skillIndex = character.skills.findIndex(skill => skill.id === skillId);
    if (skillIndex === -1) return false;

    character.skills[skillIndex] = updatedSkill;
    this.emit('skillChanged', { characterId, skillId, skill: updatedSkill });
    return true;
  }

  // 获取玩家布阵
  getPlayerFormation() {
    return this.state.player.formation || [];
  }

  // 设置玩家布阵
  setPlayerFormation(formation) {
    // 深拷贝布阵数据，避免引用问题
    this.state.player.formation = formation.map(char => ({
      id: char.id,
      position: char.position || 0
    }));

    // 保存游戏状态到本地
    this.saveGameState();

    // 同步到云端（如果已初始化云服务）
    if (window.game && game.cloudInited && typeof game.updateUserData === 'function') {
      console.log('同步布阵数据到云端');
      game.updateUserData();
    } else {
      console.log('云服务未初始化或updateUserData方法不可用，布阵数据仅保存到本地');
    }

    // 触发布阵变更事件
    this.emit('formationChanged', this.state.player.formation);

    return this.state.player.formation;
  }

  // 保存玩家数据（用于奖励系统）
  savePlayerData() {
    // 保存游戏状态到本地存储
    this.saveGameState();
    return true;
  }

  // 添加资源到玩家账户
  addResources(resources) {
    if (!resources) return false;

    // 更新资源数据
    if (resources.xianyu) {
      this.state.player.resources.xianyu += resources.xianyu;
    }

    if (resources.lingshi) {
      this.state.player.resources.lingshi += resources.lingshi;
    }

    // 保存游戏状态
    this.saveGameState();
    return true;
  }

  /**
   * 处理玩家充值
   * @param {number} amount 充值金额（元）
   * @param {number} xianyuAmount 获得的仙玉数量
   * @returns {boolean} 是否充值成功
   */
  processRecharge(amount, xianyuAmount) {
    if (!amount || amount <= 0 || !xianyuAmount || xianyuAmount <= 0) {
      console.error('充值参数无效:', amount, xianyuAmount);
      return false;
    }

    try {
      console.log('充值前状态:', {
        totalRecharge: this.state.player.totalRecharge || 0,
        vipLevel: this.state.player.vipLevel || 0
      });

      // 更新总充值金额
      this.state.player.totalRecharge = (this.state.player.totalRecharge || 0) + amount;

      console.log('更新总充值金额后:', this.state.player.totalRecharge);

      // 根据总充值金额更新VIP等级
      const newVIPLevel = this.updateVIPLevel();
      console.log('更新VIP等级后:', newVIPLevel);

      // 添加仙玉
      this.addResources({ xianyu: xianyuAmount });

      // 触发充值事件
      this.emit('playerRecharged', { amount, xianyuAmount });

      // 确保保存游戏状态
      this.saveGameState();

      console.log('充值后状态:', {
        totalRecharge: this.state.player.totalRecharge,
        vipLevel: this.state.player.vipLevel
      });

      return true;
    } catch (error) {
      console.error('处理充值时出错:', error);
      return false;
    }
  }

  /**
   * 更新玩家VIP等级
   * @returns {number} 更新后的VIP等级
   */
  updateVIPLevel() {
    try {
      // 获取VIP系统
      const vipSystem = game.vipSystem;
      if (!vipSystem) return this.state.player.vipLevel || 0;

      // 获取总充值金额
      const totalRecharge = this.state.player.totalRecharge || 0;

      // 计算VIP等级
      const newVIPLevel = vipSystem.calculateVIPLevel(totalRecharge);

      // 如果VIP等级提升，触发事件
      if (newVIPLevel > (this.state.player.vipLevel || 0)) {
        this.emit('vipLevelUp', { oldLevel: this.state.player.vipLevel, newLevel: newVIPLevel });
      }

      // 更新VIP等级
      this.state.player.vipLevel = newVIPLevel;

      // 保存游戏状态
      this.saveGameState();

      return newVIPLevel;
    } catch (error) {
      console.error('更新VIP等级时出错:', error);
      return this.state.player.vipLevel || 0;
    }
  }

  /**
   * 获取玩家VIP等级
   * @returns {number} VIP等级
   */
  getVIPLevel() {
    return this.state.player.vipLevel || 0;
  }

  /**
   * 获取玩家总充值金额
   * @returns {number} 总充值金额
   */
  getTotalRecharge() {
    return this.state.player.totalRecharge || 0;
  }

  /**
   * 领取每日VIP奖励
   * @returns {Object|null} 奖励内容或null（如果不能领取）
   */
  claimDailyVIPReward() {
    try {
      // 获取VIP系统
      const vipSystem = game.vipSystem;
      if (!vipSystem) {
        console.error('无法获取VIP系统');
        return null;
      }

      // 检查是否可以领取奖励
      if (!vipSystem.shouldGiveDailyReward(this.state.player)) {
        console.log('不满足领取每日VIP奖励条件');
        return null;
      }

      // 发放奖励
      const rewards = vipSystem.giveDailyVIPReward(this.state.player);
      if (!rewards) {
        console.error('获取VIP奖励失败');
        return null;
      }

      // 更新领取时间
      this.state.player.lastVIPRewardTime = new Date().toISOString();

      // 添加资源
      this.addResources(rewards);

      // 触发领取奖励事件
      this.emit('vipRewardClaimed', rewards);

      console.log('成功领取VIP奖励:', rewards);

      // 保存游戏状态
      this.saveGameState();

      return rewards;
    } catch (error) {
      console.error('领取VIP奖励时出错:', error);
      return null;
    }
  }

  // 保存游戏状态
  saveGameState() {
    try {
      // 在保存之前，确保装备数据正确处理
      this.state.characters.forEach(character => {
        // 如果角色使用了新的equipment对象格式但没有旧格式，确保兼容性处理
        if (character.equipment && !character.equipments) {
          character.equipments = [null, null, null, null];
        }
      });

      const gameState = JSON.stringify(this.state);
      wx.setStorageSync('gameState', gameState);

      // 同步到云端（如果已初始化云服务）
      if (window.game && game.cloudInited && typeof game.updateUserData === 'function') {
        console.log('同步游戏状态数据到云端');
        game.updateUserData();
      }

      return true;
    } catch (e) {
      console.error('保存游戏状态失败', e);
      return false;
    }
  }

  // 清空游戏状态
  clearGameState() {
    // 清空游戏状态
    this.state = {
      player: {
        nickname: '修仙者',
        avatarUrl: '',
        dongfuLevel: 1,
        resources: {
          xianyu: 1000,
          lingshi: 1000,
          swordIntent: 0
        },
        formation: []
      },
      characters: [],
      items: [],
      equipments: [],
      monsters: [],
      swordHearts: null,
      server: {
        id: 1,
        name: '太虚境'
      }
    };

    // 重新初始化剑心管理器
    this.swordHeartManager = new SwordHeartManager();

    // 重新初始化竞技场管理器
    this.arenaManager = new ArenaManager();

    // 重新初始化剑骨管理器
    this.swordBoneManager = new SwordBoneManager();

    // 保存到本地
    this.saveGameState();

    // 触发事件
    this.emit('gameStateCleared', null);

    return true;
  }

  /**
   * 获取玩家OpenID
   * @returns {string|null} 玩家OpenID或null
   */
  getPlayerOpenID() {
    // 尝试从游戏对象获取用户信息
    if (window.game && game.user && game.user.openid) {
      console.log('从game.user中获取到openid:', game.user.openid);

      // 同时确保state中的player也存储了这个openid
      if (this.state.player) {
        this.state.player.openid = game.user.openid;
      }

      return game.user.openid;
    }

    // 尝试从wx对象获取OpenID（如果已经登录过）
    if (typeof wx !== 'undefined') {
      try {
        const userInfo = wx.getStorageSync('userInfo');
        if (userInfo && userInfo.openid) {
          console.log('从wx.getStorageSync中获取到openid:', userInfo.openid);

          // 同步到game.user
          if (window.game) {
            game.user = game.user || {};
            game.user.openid = userInfo.openid;
          }

          // 同步到state.player
          if (this.state.player) {
            this.state.player.openid = userInfo.openid;
          }

          return userInfo.openid;
        }
      } catch (err) {
        console.error('获取本地用户信息失败:', err);
      }
    }

    // 如果在state中保存了openid也可以返回
    if (this.state.player && this.state.player.openid) {
      console.log('从state.player中获取到openid:', this.state.player.openid);

      // 同步到game.user
      if (window.game) {
        game.user = game.user || {};
        game.user.openid = this.state.player.openid;
      }

      return this.state.player.openid;
    }

    console.warn('未找到有效的玩家OpenID，请确保玩家已登录');
    // 尝试输出当前game.user的内容，帮助调试
    if (window.game && game.user) {
      console.log('当前game.user内容:', JSON.stringify(game.user));
    }
    return null;
  }

  /**
   * 获取完整游戏状态（用于保存到云端）
   * @returns {Object} 完整游戏状态
   */
  getFullStateForSave() {
    // 创建一个深拷贝，避免修改原始数据
    const fullState = JSON.parse(JSON.stringify(this.state));

    // 添加额外信息
    fullState.version = '1.0.0'; // 游戏版本
    fullState.timestamp = Date.now(); // 保存时间戳
    fullState.deviceInfo = this.getDeviceInfo(); // 设备信息

    // 如果有用户ID，确保它被包含在数据中
    const openid = this.getPlayerOpenID();
    if (openid) {
      fullState.openid = openid;
    }

    // 添加剑心数据
    if (this.swordHeartManager) {
      fullState.swordHearts = this.swordHeartManager.toJSON();
    }

    // 添加竞技场数据
    if (this.arenaManager) {
      fullState.arena = this.arenaManager.toJSON();
    }

    // 添加剑骨数据
    if (this.swordBoneManager) {
      fullState.swordBone = this.swordBoneManager.toJSON();
    }

    return fullState;
  }

  /**
   * 获取设备信息
   * @returns {Object} 设备信息
   */
  getDeviceInfo() {
    const deviceInfo = {
      platform: 'unknown'
    };

    // 如果在微信环境下，获取更多设备信息
    if (typeof wx !== 'undefined' && wx.getSystemInfoSync) {
      try {
        const sysInfo = wx.getSystemInfoSync();
        deviceInfo.platform = sysInfo.platform;
        deviceInfo.brand = sysInfo.brand;
        deviceInfo.model = sysInfo.model;
        deviceInfo.system = sysInfo.system;
        deviceInfo.SDKVersion = sysInfo.SDKVersion;
      } catch (err) {
        console.error('获取系统信息失败:', err);
      }
    }

    return deviceInfo;
  }

  /**
   * 序列化方法（兼容JSON.stringify）
   * @returns {Object} 序列化后的对象
   */
  toJSON() {
    return this.getFullStateForSave();
  }

  /**
   * 获取剑心管理器
   * @returns {SwordHeartManager} 剑心管理器实例
   */
  getSwordHeartManager() {
    return this.swordHeartManager;
  }

  /**
   * 获取所有剑心
   * @returns {Array} 剑心列表
   */
  getAllSwordHearts() {
    return this.swordHeartManager.getAllSwordHearts();
  }

  /**
   * 获取已解锁的剑心
   * @returns {Array} 已解锁的剑心列表
   */
  getUnlockedSwordHearts() {
    return this.swordHeartManager.getUnlockedSwordHearts();
  }

  /**
   * 获取指定ID的剑心
   * @param {string} id 剑心ID
   * @returns {SwordHeart|null} 剑心实例或null
   */
  getSwordHeart(id) {
    return this.swordHeartManager.getSwordHeart(id);
  }

  /**
   * 解锁剑心
   * @param {string} id 剑心ID
   * @returns {boolean} 是否成功解锁
   */
  unlockSwordHeart(id) {
    const result = this.swordHeartManager.unlockSwordHeart(id);
    if (result) {
      // 保存剑心数据到游戏状态
      this.state.swordHearts = this.swordHeartManager.toJSON();
      this.saveGameState();

      // 触发事件
      this.emit('swordHeartUnlocked', this.swordHeartManager.getSwordHeart(id));
    }
    return result;
  }

  /**
   * 提升剑心等级
   * @param {string} id 剑心ID
   * @param {number} swordIntent 要消耗的剑意值
   * @returns {boolean} 是否成功提升等级
   */
  levelUpSwordHeart(id, swordIntent) {
    // 检查玩家剑意是否足够
    if (this.state.player.resources.swordIntent < swordIntent) {
      return false;
    }

    // 扣除剑意
    this.state.player.resources.swordIntent -= swordIntent;

    // 提升剑心等级
    const result = this.swordHeartManager.levelUpSwordHeart(id, swordIntent);
    if (result) {
      // 保存剑心数据到游戏状态
      this.state.swordHearts = this.swordHeartManager.toJSON();
      this.saveGameState();

      // 触发事件
      this.emit('swordHeartLevelUp', this.swordHeartManager.getSwordHeart(id));
    } else {
      // 如果升级失败，返还剑意
      this.state.player.resources.swordIntent += swordIntent;
    }

    return result;
  }

  /**
   * 进阶剑心
   * @param {string} id 剑心ID
   * @param {Array} materials 材料列表
   * @returns {boolean} 是否成功进阶
   */
  advanceSwordHeart(id, materials) {
    // 检查材料是否足够
    for (const material of materials) {
      const item = this.getItem(material.id);
      if (!item || item.count < material.count) {
        return false;
      }
    }

    // 扣除材料
    for (const material of materials) {
      this.removeItem(material.id, material.count);
    }

    // 进阶剑心
    const result = this.swordHeartManager.advanceSwordHeart(id, materials);
    if (result) {
      // 保存剑心数据到游戏状态
      this.state.swordHearts = this.swordHeartManager.toJSON();
      this.saveGameState();

      // 触发事件
      this.emit('swordHeartAdvanced', this.swordHeartManager.getSwordHeart(id));
    } else {
      // 如果进阶失败，返还材料
      for (const material of materials) {
        this.addItem(new Item({
          id: material.id,
          count: material.count
        }));
      }
    }

    return result;
  }

  /**
   * 增加剑意值
   * @param {number} amount 增加的数量
   */
  addSwordIntent(amount) {
    if (!amount || amount <= 0) return;

    this.state.player.resources.swordIntent = (this.state.player.resources.swordIntent || 0) + amount;
    this.saveGameState();

    // 触发事件
    this.emit('swordIntentChanged', this.state.player.resources.swordIntent);
  }

  /**
   * 获取当前剑意值
   * @returns {number} 剑意值
   */
  getSwordIntent() {
    return this.state.player.resources.swordIntent || 0;
  }

  /**
   * 保存剑骨数据
   */
  saveSwordBoneData() {
    if (this.swordBoneManager) {
      this.state.swordBone = this.swordBoneManager.toJSON();
      this.saveGameState();
    }
  }

  /**
   * 添加指定类型的资源
   * @param {string} resourceType 资源类型（xianyu, lingshi, swordIntent, swordYuan等）
   * @param {number} amount 添加的数量（可以为负数，表示减少）
   * @returns {boolean} 是否成功添加资源
   */
  addResource(resourceType, amount) {
    if (!amount || amount === 0) return true;

    // 确保player和resources对象存在
    if (!this.state.player) {
      this.state.player = {};
    }

    if (!this.state.player.resources) {
      this.state.player.resources = {};
    }

    // 如果资源不存在，初始化为0
    if (this.state.player.resources[resourceType] === undefined) {
      this.state.player.resources[resourceType] = 0;
    }

    // 如果是减少资源，检查是否足够
    if (amount < 0 && this.state.player.resources[resourceType] < Math.abs(amount)) {
      return false;
    }

    // 添加资源
    this.state.player.resources[resourceType] += amount;

    // 保存游戏状态
    this.saveGameState();

    // 触发资源变化事件
    this.emit('resourceChanged', {
      type: resourceType,
      amount: amount,
      total: this.state.player.resources[resourceType]
    });

    return true;
  }

  /**
   * 获取指定类型的资源数量
   * @param {string} resourceType 资源类型
   * @returns {number} 资源数量
   */
  getResourceAmount(resourceType) {
    if (!this.state.player || !this.state.player.resources) {
      return 0;
    }

    return this.state.player.resources[resourceType] || 0;
  }

  /**
   * 获取抽卡保底计数器
   * @returns {number} 当前保底计数
   */
  getGachaPity() {
    return this.state.gachaPity || 0;
  }

  /**
   * 更新抽卡保底计数器
   * @param {number} count 新的保底计数值或增加的计数值
   * @param {boolean} isIncrement 是否为增量更新，默认为false（直接设置）
   * @returns {number} 更新后的保底计数
   */
  updateGachaPity(count, isIncrement = false) {
    if (isIncrement) {
      this.state.gachaPity = (this.state.gachaPity || 0) + count;
    } else {
      this.state.gachaPity = count;
    }

    // 触发事件
    this.emit('gachaPityChanged', this.state.gachaPity);

    // 保存游戏状态
    this.saveGameState();

    return this.state.gachaPity;
  }
}

export default GameStateManager;