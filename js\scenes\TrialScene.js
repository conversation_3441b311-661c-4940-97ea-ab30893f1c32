/**
 * 试炼场景类
 * 提供各类试炼挑战，获取资源奖励
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import game from '../../game';
import BattleManager from '../battle/BattleManager';

class TrialScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager,resources) {
    super(ctx, screenWidth, screenHeight, sceneManager);

    // 场景资源
    this.resources = resources || {};

    // 当前选中的底部导航项
    this.selectedTabIndex = 3; // 试炼对应的导航索引

    // 试炼关卡数据
    this.trials = [
      { id: 1, name: '凡人试炼', level: 1, reward: { lingshi: 100, exp: 50, forgeStone: 100 }, completed: false },
      { id: 2, name: '修士试炼', level: 5, reward: { lingshi: 300, exp: 150 }, completed: false },
      { id: 3, name: '真人试炼', level: 10, reward: { lingshi: 500, exp: 250 }, completed: false },
      { id: 4, name: '金丹试炼', level: 20, reward: { lingshi: 1000, exp: 500 }, completed: false },
      { id: 5, name: '元婴试炼', level: 30, reward: { lingshi: 2000, exp: 1000 }, completed: false }
    ];

    // 剑心试炼关卡数据
    this.swordHeartTrials = [
      { id: 101, name: '初级剑心试炼', level: 1, reward: { swordIntent: 100 }, completed: false },
      { id: 102, name: '中级剑心试炼', level: 10, reward: { swordIntent: 300 }, completed: false },
      { id: 103, name: '高级剑心试炼', level: 20, reward: { swordIntent: 600 }, completed: false },
      { id: 104, name: '剑心真解试炼', level: 30, reward: { swordIntent: 1000 }, completed: false },
      { id: 105, name: '剑心奥义试炼', level: 40, reward: { swordIntent: 2000, swordHeartId: 'hantie' }, completed: false }
    ];

    // 当前试炼类型（青禾秘境或剑心试炼）
    this.trialType = 'qinghe'; // 'qinghe' 或 'swordHeart'

    // 移除在构造函数中对initUI的调用
    // 初始化UI将在onShow方法中调用
  }

  // 初始化UI
  initUI() {

    // 底部导航栏按钮
    const tabBarHeight = 60;
    const tabBarY = this.screenHeight - tabBarHeight;
    const tabButtonWidth = this.screenWidth / 5;

    // 创建底部导航栏按钮
    this.tabButtons = [
      this.createTabButton(0 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '主页', 0),
      this.createTabButton(1 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '角色', 1),
      this.createTabButton(2 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '洞府', 2),
      this.createTabButton(3 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '试炼', 3),
      this.createTabButton(4 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '背包', 4)
    ];

    // 将按钮添加到UI元素列表
    this.tabButtons.forEach(button => {
      this.addUIElement(button);
    });

    // 创建试炼类型切换按钮
    this.createTrialTypeButtons();

    // 创建试炼关卡按钮
    this.createTrialButtons();
  }

  // 创建试炼类型切换按钮
  createTrialTypeButtons() {
    const headerHeight = 80;
    const buttonWidth = 120;
    const buttonHeight = 40;
    const margin = 20;

    // 青禾秘境按钮
    this.qingheButton = new Button(
      this.ctx,
      this.screenWidth / 2 - buttonWidth - margin / 2,
      headerHeight + 10,
      buttonWidth,
      buttonHeight,
      '青禾秘境',
      null,
      this.trialType === 'qinghe' ? '#4299e1' : null,
      () => {
        this.trialType = 'qinghe';
        this.updateTrialTypeButtons();
        this.createTrialButtons();
      }
    );

    this.addUIElement(this.qingheButton);

    // 剑心试炼按钮
    this.swordHeartButton = new Button(
      this.ctx,
      this.screenWidth / 2 + margin / 2,
      headerHeight + 10,
      buttonWidth,
      buttonHeight,
      '剑心试炼',
      null,
      this.trialType === 'swordHeart' ? '#4299e1' : null,
      () => {
        this.trialType = 'swordHeart';
        this.updateTrialTypeButtons();
        this.createTrialButtons();
      }
    );

    this.addUIElement(this.swordHeartButton);
  }

  // 更新试炼类型按钮状态
  updateTrialTypeButtons() {
    this.qingheButton.pressedImg = this.trialType === 'qinghe' ? '#4299e1' : null;
    this.swordHeartButton.pressedImg = this.trialType === 'swordHeart' ? '#4299e1' : null;
  }

  // 创建试炼关卡按钮
  createTrialButtons() {
    const headerHeight = 80;
    const buttonWidth = 200;
    const buttonHeight = 50;
    const buttonMargin = 10;

    // 清除之前的按钮
    if (this.trialButtons && this.trialButtons.length > 0) {
      this.trialButtons.forEach(button => {
        this.removeUIElement(button);
      });
    }

    this.trialButtons = [];

    // 根据当前试炼类型选择关卡数据
    const trialsData = this.trialType === 'qinghe' ? this.trials : this.swordHeartTrials;

    // 创建关卡按钮
    trialsData.forEach((trial, index) => {
      const button = new Button(
        this.ctx,
        (this.screenWidth - buttonWidth) / 2,
        headerHeight + 70 + index * (buttonHeight + buttonMargin),
        buttonWidth,
        buttonHeight,
        trial.name,
        null,
        null,
        () => {
          if (this.trialType === 'qinghe') {
            this.startTrial(trial.id);
          } else {
            this.startSwordHeartTrial(trial.id);
          }
        }
      );

      this.trialButtons.push(button);
      this.addUIElement(button);
    });
  }

  // 创建底部导航栏按钮
  createTabButton(x, y, width, height, text, index) {
    return new Button(
      this.ctx,
      x,
      y,
      width,
      height,
      text,
      null,
      null,
      () => {
        this.onTabSelected(index);
      }
    );
  }

  // 底部导航栏选中回调
  onTabSelected(index) {
    // 如果点击的是当前选中的项，不做处理
    if (this.selectedTabIndex === index) {
      return;
    }

    // 更新选中的索引
    this.selectedTabIndex = index;

    // 根据索引切换场景
    switch (index) {
      case 0:
        // 主页
        this.sceneManager.showScene('main');
        break;
      case 1:
        // 角色页面 - 直接进入女剑仙角色详情页
        this.sceneManager.showScene('characterDetail', { characterId: 1 });
        break;
      case 2:
        // 洞府页面
        this.sceneManager.showScene('dongfu');
        break;
      case 3:
        // 试炼页面，已经在试炼页面，不需要切换
        break;
      case 4:
        // 背包页面
        this.sceneManager.showScene('backpack');
        break;
    }
  }

  // 开始青禾秘境试炼
  startTrial(trialId) {
    // 获取试炼关卡
    const trial = this.trials.find(t => t.id === trialId);
    if (!trial) return;

    // 获取角色列表
    const characters = game.gameStateManager.getCharacters();
    if (characters.length === 0) {
      wx.showToast({
        title: '没有可用角色，无法进行试炼！',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 获取战力最高的角色
    const strongestCharacter = characters.reduce((prev, current) => {
      return (prev.power > current.power) ? prev : current;
    });

    // 检查角色等级是否达到要求
    if (strongestCharacter.level < trial.level) {
      wx.showToast({
        title: `角色等级不足，需要等级${trial.level}以上的角色才能挑战！`,
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 计算关卡战力 (基于关卡等级)
    const stagePower = trial.level * 100; // 每级100点战力

    // 获取玩家总战力
    const playerPower = strongestCharacter.power || 0;

    console.log(`试炼对比 - 角色战力: ${playerPower}, 关卡战力: ${stagePower}`);

    // 直接对比战力，无需进入战斗场景
    if (playerPower >= stagePower) {
      // 战斗胜利
      console.log('战斗胜利，显示奖励');

      // 准备奖励数据
      const rewards = {
        lingshi: trial.reward ? trial.reward.lingshi : 100, // 灵石奖励
        xianyu: 10,  // 仙玉奖励
        exp: trial.reward ? trial.reward.exp : 50, // 经验奖励
        forgeStone: trial.reward ? trial.reward.forgeStone : 0 // 炼器石奖励
      };

      // 直接处理炼器石奖励（添加到玩家物品中）
      if (rewards.forgeStone > 0) {
        const player = game.gameStateManager.getPlayer();
        // 查找是否已有炼器石物品
        let forgeStoneItem = player.items ? player.items.find(item => item.name === '炼器石') : null;

        if (forgeStoneItem) {
          // 如果已有，增加数量
          forgeStoneItem.count += rewards.forgeStone;
        } else {
          // 如果没有，创建新物品
          if (!player.items) {
            player.items = [];
          }
          player.items.push({
            id: 'forgeStone_' + Date.now(),
            name: '炼器石',
            type: 'material',
            quality: 0, // 普通品质
            count: rewards.forgeStone,
            stackable: true,
            description: '用于锻造装备的材料'
          });
        }
        // 保存玩家数据
        game.gameStateManager.setPlayer(player);
      }

      // 显示战斗结果界面
      this.sceneManager.showScene('battleResult', {
        result: { victory: true },
        rewards: rewards,
        onComplete: () => {
          // 关闭结果界面后返回试炼界面
          this.sceneManager.showScene('trial');

          // 标记试炼为已完成
          trial.completed = true;
        }
      });
    } else {
      // 战斗失败
      wx.showToast({
        title: `战力不足，无法挑战该关卡！需要提升战力至少至${stagePower}点`,
        icon: 'none',
        duration: 2000
      });
    }
  }

  // 开始剑心试炼
  startSwordHeartTrial(trialId) {
    // 获取试炼关卡
    const trial = this.swordHeartTrials.find(t => t.id === trialId);
    if (!trial) return;

    // 获取角色列表
    const characters = game.gameStateManager.getCharacters();
    if (characters.length === 0) {
      wx.showToast({
        title: '没有可用角色，无法进行试炼！',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 获取女剑仙角色（主角）
    const mainCharacter = characters.find(char => char.id === 1);
    if (!mainCharacter) {
      wx.showToast({
        title: '女剑仙角色不存在，无法进行剑心试炼！',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 检查角色等级是否达到要求
    if (mainCharacter.level < trial.level) {
      wx.showToast({
        title: `角色等级不足，需要等级${trial.level}以上的角色才能挑战！`,
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 计算关卡战力 (基于关卡等级)
    const stagePower = trial.level * 120; // 剑心试炼难度略高

    // 获取玩家总战力
    const playerPower = mainCharacter.power || 0;

    console.log(`剑心试炼对比 - 角色战力: ${playerPower}, 关卡战力: ${stagePower}`);

    // 直接对比战力，无需进入战斗场景
    if (playerPower >= stagePower) {
      // 战斗胜利
      console.log('剑心试炼胜利，显示奖励');

      // 准备奖励数据
      const rewards = {
        swordIntent: trial.reward ? trial.reward.swordIntent : 100 // 剑意奖励
      };

      // 处理剑意奖励
      if (rewards.swordIntent > 0) {
        game.gameStateManager.addSwordIntent(rewards.swordIntent);
      }

      // 处理剑心解锁奖励
      if (trial.reward.swordHeartId) {
        game.gameStateManager.unlockSwordHeart(trial.reward.swordHeartId);
      }

      // 显示战斗结果界面
      this.sceneManager.showScene('battleResult', {
        result: { victory: true },
        rewards: rewards,
        onComplete: () => {
          // 关闭结果界面后返回试炼界面
          this.sceneManager.showScene('trial');

          // 标记试炼为已完成
          trial.completed = true;
        }
      });
    } else {
      // 战斗失败
      wx.showToast({
        title: `战力不足，无法挑战该关卡！需要提升战力至少至${stagePower}点`,
        icon: 'none',
        duration: 2000
      });
    }
  }

  // 场景显示时的回调
  onShow(params) {
    // 清空UI元素
    this.clearUIElements();

    // 初始化UI（现在在onShow中调用，而不是构造函数中）
    this.initUI();

    // 更新选中的导航项
    this.selectedTabIndex = 3;
  }

  // 子类实现的绘制逻辑
  drawScene() {
    // 绘制背景
    this.drawBackground();

    // 绘制顶部导航栏
    this.drawHeader();

    // 绘制试炼信息
    this.drawTrialInfo();

    // 绘制底部导航栏
    this.drawTabBar();
  }

  // 绘制背景
  drawBackground() {
    // 使用渐变色背景
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
    gradient.addColorStop(0, '#000000');
    gradient.addColorStop(1, '#434343');

    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
  }

  // 绘制顶部导航栏
  drawHeader() {
    const headerHeight = 80;

    // 绘制顶部导航栏背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, 0, this.screenWidth, headerHeight);

    // 绘制头像框
    const avatarSize = 60;
    const avatarX = 10;
    const avatarY = 10;

    // 如果有头像资源，绘制头像
    if (this.resources && this.resources.avatarFrame) {
      try {
        this.ctx.drawImage(
          this.resources.avatarFrame,
          avatarX,
          avatarY,
          avatarSize,
          avatarSize
        );
      } catch (error) {
        console.error('绘制头像框失败', error);
        this.drawDefaultAvatarFrame(avatarX, avatarY, avatarSize);
      }

      // 尝试获取玩家微信头像
      const avatarUrl = game.gameStateManager.getPlayer().avatarUrl;
      if (avatarUrl) {
        // 加载玩家头像
        const avatar = wx.createImage();
        avatar.onload = () => {
          // 绘制圆形头像
          this.ctx.save();
          this.ctx.beginPath();
          this.ctx.arc(avatarX + avatarSize / 2, avatarY + avatarSize / 2, avatarSize / 2 - 5, 0, Math.PI * 2);
          this.ctx.closePath();
          this.ctx.clip();

          this.ctx.drawImage(avatar, avatarX + 5, avatarY + 5, avatarSize - 10, avatarSize - 10);

          this.ctx.restore();
        };
        avatar.src = avatarUrl;
      } else {
        // 如果没有头像，绘制默认头像
        this.ctx.fillStyle = '#ffffff';
        this.ctx.beginPath();
        this.ctx.arc(avatarX + avatarSize / 2, avatarY + avatarSize / 2, avatarSize / 2 - 5, 0, Math.PI * 2);
        this.ctx.fill();
      }
    } else {
      // 如果没有头像框资源，绘制简单的圆形头像框
      this.drawDefaultAvatarFrame(avatarX, avatarY, avatarSize);
    }

    // 绘制玩家信息
    const player = game.gameStateManager.getPlayer();

    // 绘制玩家昵称
    this.ctx.font = '18px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(player.nickname, avatarX + avatarSize + 10, avatarY + 20);

    // 绘制洞府等级
    this.ctx.font = '16px Arial';
    this.ctx.fillText(`洞府等级: ${player.dongfuLevel}`, avatarX + avatarSize + 10, avatarY + 45);

    // 绘制资源信息
    const resourceY = headerHeight / 2;
    const iconSize = 20;

    // 绘制仙玉
    this.drawResourceIcon('iconXianyu', this.screenWidth / 2, resourceY, iconSize, '#ffd700');

    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`仙玉: ${player.resources.xianyu}`, this.screenWidth / 2 + iconSize + 5, resourceY + 5);

    // 绘制灵石
    const lingshiX = this.screenWidth / 2 + 120;
    this.drawResourceIcon('iconLingshi', lingshiX, resourceY, iconSize, '#c0c0c0');

    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`灵石: ${player.resources.lingshi}`, lingshiX + iconSize + 5, resourceY + 5);
  }

  // 绘制默认头像框
  drawDefaultAvatarFrame(x, y, size) {
    this.ctx.strokeStyle = '#ffffff';
    this.ctx.lineWidth = 2;
    this.ctx.beginPath();
    this.ctx.arc(x + size / 2, y + size / 2, size / 2, 0, Math.PI * 2);
    this.ctx.stroke();
  }

  // 绘制资源图标
  drawResourceIcon(iconKey, x, y, size, fallbackColor) {
    if (this.resources && this.resources[iconKey]) {
      try {
        this.ctx.drawImage(
          this.resources[iconKey],
          x,
          y - size / 2,
          size,
          size
        );
      } catch (error) {
        console.error(`绘制图标 ${iconKey} 失败`, error);
        this.drawDefaultIcon(x, y, size, fallbackColor);
      }
    } else {
      this.drawDefaultIcon(x, y, size, fallbackColor);
    }
  }

  // 绘制默认图标
  drawDefaultIcon(x, y, size, color) {
    // 如果是圆形图标
    if (color === '#ffd700') { // 仙玉用圆形
      this.ctx.fillStyle = color;
      this.ctx.beginPath();
      this.ctx.arc(x + size / 2, y, size / 2, 0, Math.PI * 2);
      this.ctx.fill();
    } else { // 灵石用方形
      this.ctx.fillStyle = color;
      this.ctx.fillRect(x, y - size / 2, size, size);
    }
  }

  // 绘制试炼信息
  drawTrialInfo() {
    const headerHeight = 80;

    // 绘制试炼标题
    this.ctx.font = 'bold 28px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';

    // 根据当前试炼类型显示不同标题
    const title = this.trialType === 'qinghe' ? '青禾秘境' : '剑心试炼';
    this.ctx.fillText(title, this.screenWidth / 2, headerHeight + 40);

    // 获取角色列表
    const characters = game.gameStateManager.getCharacters();
    let strongestCharacter = null;

    if (characters.length > 0) {
      // 获取战力最高的角色
      strongestCharacter = characters.reduce((prev, current) => {
        return (prev.power > current.power) ? prev : current;
      });
    }

    // 根据当前试炼类型选择关卡数据
    const trialsData = this.trialType === 'qinghe' ? this.trials : this.swordHeartTrials;

    // 绘制试炼关卡信息
    trialsData.forEach((trial, index) => {
      const y = headerHeight + 70 + index * 60;

      // 绘制关卡信息背景
      this.ctx.fillStyle = trial.completed ? 'rgba(0, 128, 0, 0.4)' : 'rgba(50, 50, 50, 0.7)';
      this.ctx.fillRect(50, y, this.screenWidth - 100, 50);

      // 绘制关卡信息
      this.ctx.font = '18px Arial';
      this.ctx.fillStyle = '#ffffff';
      this.ctx.textAlign = 'left';
      this.ctx.fillText(`${trial.name}`, 70, y + 20);
      this.ctx.fillText(`等级要求: ${trial.level}`, 70, y + 40);

      // 绘制奖励信息
      this.ctx.textAlign = 'right';

      if (this.trialType === 'qinghe') {
        // 青禾秘境奖励
        this.ctx.fillText(`奖励: ${trial.reward.lingshi}灵石, ${trial.reward.exp}经验, ${trial.reward.forgeStone || 0}炼器石`, this.screenWidth - 70, y + 30);
      } else {
        // 剑心试炼奖励
        let rewardText = `奖励: ${trial.reward.swordIntent}剑意`;
        if (trial.reward.swordHeartId) {
          const swordHeart = game.gameStateManager.getSwordHeart(trial.reward.swordHeartId);
          if (swordHeart) {
            rewardText += `, ${swordHeart.name}`;
          }
        }
        this.ctx.fillText(rewardText, this.screenWidth - 70, y + 30);
      }

      // 绘制试炼状态
      if (trial.completed) {
        this.ctx.font = 'bold 16px Arial';
        this.ctx.fillStyle = '#00ff00';
        this.ctx.fillText('已完成', this.screenWidth - 70, y + 50);
      } else if (strongestCharacter && strongestCharacter.level >= trial.level) {
        this.ctx.font = 'bold 16px Arial';
        this.ctx.fillStyle = '#ffff00';
        this.ctx.fillText('可挑战', this.screenWidth - 70, y + 50);
      } else {
        this.ctx.font = 'bold 16px Arial';
        this.ctx.fillStyle = '#ff0000';
        this.ctx.fillText('等级不足', this.screenWidth - 70, y + 50);
      }
    });

    // 绘制提示信息
    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';

    const tipY = headerHeight + 70 + this.trials.length * 60 + 20;

    if (characters.length === 0) {
      this.ctx.fillText('你还没有角色，请先招募角色！', this.screenWidth / 2, tipY);
    } else if (strongestCharacter) {
      this.ctx.fillText(`你的最强角色: ${strongestCharacter.name}, 等级: ${strongestCharacter.level}, 战力: ${strongestCharacter.power}`, this.screenWidth / 2, tipY);
    }
  }

  // 绘制底部导航栏
  drawTabBar() {
    const tabBarHeight = 60;
    const tabBarY = this.screenHeight - tabBarHeight;

    // 绘制底部导航栏背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, tabBarY, this.screenWidth, tabBarHeight);

    // 绘制分割线
    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
    this.ctx.lineWidth = 1;

    for (let i = 1; i < 5; i++) {
      const x = this.screenWidth * i / 5;
      this.ctx.beginPath();
      this.ctx.moveTo(x, tabBarY);
      this.ctx.lineTo(x, this.screenHeight);
      this.ctx.stroke();
    }

    // 绘制选中项的高亮背景
    const tabWidth = this.screenWidth / 5;
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
    this.ctx.fillRect(this.selectedTabIndex * tabWidth, tabBarY, tabWidth, tabBarHeight);

    // 导航栏文字
    const tabTexts = ['主页', '角色', '洞府', '试炼', '背包'];

    // 绘制导航栏图标和文字
    for (let i = 0; i < 5; i++) {
      const tabX = i * tabWidth + tabWidth / 2;

      // 绘制图标
      const iconSize = 30;
      const iconY = tabBarY + 10;

      // 如果有图标资源，绘制图标
      const iconKey = `tabIcon${i + 1}`;
      if (this.resources && this.resources[iconKey]) {
        this.ctx.drawImage(
          this.resources[iconKey],
          tabX - iconSize / 2,
          iconY,
          iconSize,
          iconSize
        );
      } else {
        // 如果没有图标资源，绘制简单的占位符
        this.ctx.fillStyle = i === this.selectedTabIndex ? '#ffffff' : '#a0a0a0';
        this.ctx.beginPath();
        this.ctx.arc(tabX, iconY + iconSize / 2, iconSize / 2, 0, Math.PI * 2);
        this.ctx.fill();
      }

      // 绘制文字
      this.ctx.font = '14px Arial';
      this.ctx.fillStyle = i === this.selectedTabIndex ? '#ffffff' : '#a0a0a0';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(tabTexts[i], tabX, tabBarY + 50);
    }
  }
}

export default TrialScene;