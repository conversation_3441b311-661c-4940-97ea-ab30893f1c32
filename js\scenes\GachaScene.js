/**
 * 角色抽卡场景
 */
import BaseScene from './BaseScene.js';
import { QUALITY, QUALITY_NAMES, QUALITY_COLORS, CHARACTER_TEMPLATES, generateCharacterAttributes, getCharactersByQuality } from '../models/CharacterTemplate.js';
import Character from '../models/Character.js';
import Button from '../ui/Button.js';
import AppContext from '../utils/AppContext.js';

export class GachaScene extends BaseScene {
    constructor(ctx, screenWidth, screenHeight, sceneManager) {
        super(ctx, screenWidth, screenHeight, sceneManager);
        this.name = 'gacha';
        this.title = '角色抽卡';
        
        // 抽卡价格（仙玉）
        this.singleDrawCost = 100;
        this.tenDrawCost = 900;  // 10连抽有优惠
        
        // 抽卡概率
        this.probabilities = {
            [QUALITY.NORMAL]: 0.40,    // 普通 40%
            [QUALITY.RARE]: 0.30,      // 稀有 30%
            [QUALITY.EPIC]: 0.15,      // 史诗 15%
            [QUALITY.LEGENDARY]: 0.10, // 传说 10%
            [QUALITY.DIVINE]: 0.05     // 神话 5%
        };
        
        // 抽卡结果
        this.drawResults = [];
        this.showingResults = false;
        
        // 保底机制
        this.pityCounter = 0;
        this.pityThreshold = 100;  // 100抽必出神话品质
        
        // 一键十连必出史诗以上品质
        this.guaranteedEpicInTenDraw = true;
        
        // 添加保底进度条UI
        this.pityProgressBar = {
            x: 50,
            y: this.screenHeight - 100,
            width: this.screenWidth - 100,
            height: 20,
            color: '#4CAF50'
        };
    }
    
    // 场景显示时的回调
    onShow(params) {
        console.log('GachaScene.onShow被调用，参数：', params);
        
        // 获取保底计数
        const gameStateManager = AppContext.game.gameStateManager;
        this.pityCounter = gameStateManager.getGachaPity() || 0;
        
        this.initUI();
        this.visible = true;
    }
    
    // 场景隐藏时的回调
    onHide() {
        // 清空UI元素
        this.clearUIElements();
        
        // 重置抽卡结果
        this.drawResults = [];
        this.showingResults = false;
        
        // 设置场景为不可见
        this.visible = false;
        
        console.log('GachaScene隐藏');
    }
    
    initUI() {
        // 清空之前的UI元素
        this.clearUIElements();
        
        if (this.showingResults) {
            this.initResultUI();
        } else {
            this.initDrawUI();
        }
    }
    
    initDrawUI() {
        const gameStateManager = AppContext.game.gameStateManager;
        const player = gameStateManager.getPlayer();
        const resources = player.resources || {};
        const xianyuAmount = resources.xianyu || 0;
        
        // 返回按钮
        const backButton = new Button(
            this.ctx,
            10,
            10,
            80,
            40,
            '返回',
            null,
            null,
            () => {
                this.sceneManager.showScene('main', { from: 'gacha' });
            }
        );
        this.addUIElement(backButton);
        
        // 单抽按钮
        const singleDrawButton = new Button(
            this.ctx,
            this.screenWidth / 2 - 150,
            this.screenHeight / 2 - 50,
            140,
            50,
            `单抽 (${this.singleDrawCost}仙玉)`,
            null,
            null,
            () => this.performDraw(1),
            false,
            xianyuAmount < this.singleDrawCost
        );
        this.addUIElement(singleDrawButton);
        
        // 十连抽按钮
        const tenDrawButton = new Button(
            this.ctx,
            this.screenWidth / 2 + 10,
            this.screenHeight / 2 - 50,
            140,
            50,
            `十连抽 (${this.tenDrawCost}仙玉)`,
            null,
            null,
            () => this.performDraw(10),
            false,
            xianyuAmount < this.tenDrawCost
        );
        this.addUIElement(tenDrawButton);
        
        // 抽卡概率按钮
        const probabilityButton = new Button(
            this.ctx,
            this.screenWidth / 2 - 70,
            this.screenHeight / 2 + 30,
            140,
            40,
            '抽卡概率',
            null,
            null,
            () => this.showProbabilities()
        );
        this.addUIElement(probabilityButton);
    }
    
    initResultUI() {
        // 返回按钮
        const backButton = new Button(
            this.ctx,
            this.screenWidth / 2 - 50,
            this.screenHeight - 60,
            100,
            40,
            '返回',
            null,
            null,
            () => {
                this.showingResults = false;
                this.drawResults = [];
                this.initUI();
            }
        );
        this.addUIElement(backButton);
    }
    
    performDraw(count) {
        const gameStateManager = AppContext.game.gameStateManager;
        const player = gameStateManager.getPlayer();
        const cost = count === 10 ? this.tenDrawCost : this.singleDrawCost * count;
        
        // 检查是否有足够的仙玉
        if (!player.resources || player.resources.xianyu < cost) {
            if (AppContext.game.uiManager) {
                AppContext.game.uiManager.showMessage('仙玉不足！');
            } else {
                console.error('仙玉不足！');
            }
            return;
        }
        
        // 扣除仙玉
        player.resources.xianyu -= cost;
        gameStateManager.setPlayer(player);
        
        // 执行抽卡
        this.drawResults = [];
        
        // 十连抽保底一个史诗以上品质
        let hasGuaranteedEpic = false;
        
        for (let i = 0; i < count; i++) {
            // 十连抽的最后一次，检查是否需要保底
            const needGuarantee = count === 10 && i === 9 && !hasGuaranteedEpic && this.guaranteedEpicInTenDraw;
            
            const result = this.drawCharacter(needGuarantee);
            this.drawResults.push(result);
            
            // 判断是否抽到了史诗以上品质
            if (result.quality >= QUALITY.EPIC) {
                hasGuaranteedEpic = true;
            }
        }
        
        // 增加抽卡次数，用于保底
        this.pityCounter += count;
        // 如果抽到了神话品质，重置保底计数
        if (this.drawResults.some(result => result.quality === QUALITY.DIVINE)) {
            this.pityCounter = 0;
        }
        gameStateManager.updateGachaPity(this.pityCounter);
        
        // 显示结果
        this.showingResults = true;
        this.initUI();
        
        // 记录抽卡历史
        this.recordDrawHistory(count);
    }
    
    recordDrawHistory(count) {
        const gameStateManager = AppContext.game.gameStateManager;
        const player = gameStateManager.getPlayer();
        if (!player.gachaHistory) {
            player.gachaHistory = {
                totalDraws: 0,
                qualityStats: {
                    [QUALITY.NORMAL]: 0,
                    [QUALITY.RARE]: 0,
                    [QUALITY.EPIC]: 0,
                    [QUALITY.LEGENDARY]: 0,
                    [QUALITY.DIVINE]: 0
                }
            };
        }
        
        player.gachaHistory.totalDraws += count;
        
        // 更新各品质抽到的数量统计
        this.drawResults.forEach(result => {
            player.gachaHistory.qualityStats[result.quality]++;
        });
        
        gameStateManager.setPlayer(player);
    }
    
    drawCharacter(guaranteeEpicOrAbove = false) {
        let quality;
        
        // 检查是否触发保底
        if (this.pityCounter >= this.pityThreshold - 1) {
            quality = QUALITY.DIVINE;
        } else if (guaranteeEpicOrAbove) {
            // 保底史诗及以上品质
            const epicPlusProb = {
                [QUALITY.EPIC]: this.probabilities[QUALITY.EPIC] / (this.probabilities[QUALITY.EPIC] + this.probabilities[QUALITY.LEGENDARY] + this.probabilities[QUALITY.DIVINE]),
                [QUALITY.LEGENDARY]: this.probabilities[QUALITY.LEGENDARY] / (this.probabilities[QUALITY.EPIC] + this.probabilities[QUALITY.LEGENDARY] + this.probabilities[QUALITY.DIVINE]),
                [QUALITY.DIVINE]: this.probabilities[QUALITY.DIVINE] / (this.probabilities[QUALITY.EPIC] + this.probabilities[QUALITY.LEGENDARY] + this.probabilities[QUALITY.DIVINE])
            };
            
            const rand = Math.random();
            let cumulativeProbability = 0;
            
            for (const [q, prob] of Object.entries(epicPlusProb)) {
                cumulativeProbability += prob;
                if (rand <= cumulativeProbability) {
                    quality = parseInt(q);
                    break;
                }
            }
        } else {
            // 随机抽取品质
            const rand = Math.random();
            let cumulativeProbability = 0;
            
            for (const [q, prob] of Object.entries(this.probabilities)) {
                cumulativeProbability += prob;
                if (rand <= cumulativeProbability) {
                    quality = parseInt(q);
                    break;
                }
            }
        }
        
        // 从该品质的角色池中随机选择一个角色
        const charactersOfQuality = getCharactersByQuality(quality);
        const randomIndex = Math.floor(Math.random() * charactersOfQuality.length);
        const selectedTemplate = charactersOfQuality[randomIndex];
        
        // 检查是否已有该角色
        const gameStateManager = AppContext.game.gameStateManager;
        const existingCharacters = gameStateManager.getCharacters() || [];
        const existingCharacter = existingCharacters.find(char => char.name === selectedTemplate.name);
        
        if (existingCharacter) {
            // 如果已有该角色，给予角色碎片
            let fragmentAmount = 10;  // 默认
            // 根据品质提供不同数量的碎片
            switch (quality) {
                case QUALITY.NORMAL:
                    fragmentAmount = 10;
                    break;
                case QUALITY.RARE:
                    fragmentAmount = 20;
                    break;
                case QUALITY.EPIC:
                    fragmentAmount = 30;
                    break;
                case QUALITY.LEGENDARY:
                    fragmentAmount = 40;
                    break;
                case QUALITY.DIVINE:
                    fragmentAmount = 50;
                    break;
            }
            
            // 更新角色碎片
            existingCharacter.fragments = (existingCharacter.fragments || 0) + fragmentAmount;
            
            // 使用updateCharacter方法更新角色数据
            gameStateManager.updateCharacter(existingCharacter.id, existingCharacter);
            
            // 同步到云端
            if (AppContext.game && AppContext.game.updateUserData) {
                console.log('同步角色碎片数据到云端');
                AppContext.game.updateUserData();
            }
            
            return {
                name: selectedTemplate.name,
                quality: selectedTemplate.quality,
                isDuplicate: true,
                fragmentAmount
            };
        } else {
            // 创建新角色并添加到玩家的角色列表
            const attributes = generateCharacterAttributes(selectedTemplate.quality, 1, 0);
            
            const newCharacter = new Character({
                id: Date.now() + Math.floor(Math.random() * 1000),
                name: selectedTemplate.name,
                quality: selectedTemplate.quality,
                level: 1,
                star: 0,
                fragments: 0,
                ...attributes
            });
            
            // 使用GameStateManager的addCharacter方法添加角色，确保触发角色变更事件
            gameStateManager.addCharacter(newCharacter);
            
            // 同步到云端
            if (AppContext.game && AppContext.game.updateUserData) {
                console.log('同步新抽取的角色数据到云端');
                AppContext.game.updateUserData();
            }
            
            return {
                name: selectedTemplate.name,
                quality: selectedTemplate.quality,
                isDuplicate: false,
                character: newCharacter
            };
        }
    }
    
    showProbabilities() {
        let message = '抽卡概率:\n\n';
        for (const quality in this.probabilities) {
            message += `${QUALITY_NAMES[quality]}: ${this.probabilities[quality] * 100}%\n`;
        }
        message += `\n保底机制: \n- ${this.pityThreshold}抽必出神话品质角色\n- 十连抽必出史诗及以上品质角色`;
        
        // 使用微信原生API显示消息框
        if (typeof wx !== 'undefined' && wx.showModal) {
            wx.showModal({
                title: '抽卡概率',
                content: message,
                showCancel: false
            });
        } else {
            console.log(message);
        }
    }
    
    // 绘制场景
    drawScene() {
        // 绘制背景
        this.drawBackground();
        
        // 标题
        this.ctx.font = '28px Arial';
        this.ctx.fillStyle = '#fff';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(this.title, this.screenWidth / 2, 30);
        
        // 绘制抽卡结果或抽卡界面
        if (this.showingResults) {
            this.displayDrawResults();
        } else {
            // 显示当前仙玉数量
            const gameStateManager = AppContext.game.gameStateManager;
            const player = gameStateManager.getPlayer();
            const resources = player.resources || {};
            const xianyuAmount = resources.xianyu || 0;
            
            this.ctx.font = '24px Arial';
            this.ctx.fillStyle = '#fff';
            this.ctx.textAlign = 'center';
            this.ctx.fillText(`仙玉: ${xianyuAmount}`, this.screenWidth / 2, 50);
            
            // 保底计数
            this.ctx.font = '16px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillText(`当前已抽${this.pityCounter}次，距离保底还剩${this.pityThreshold - this.pityCounter}次`, 
                             this.screenWidth / 2, this.screenHeight / 2 + 100);
        }
        
        // 绘制保底进度条
        this.drawPityProgress();
        
        // 显示保底详细信息
        this.displayPityInfo();
    }
    
    // 绘制背景
    drawBackground() {
        // 创建渐变背景
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
        gradient.addColorStop(0, '#1a2a3d');
        gradient.addColorStop(1, '#0d1824');
        
        // 填充背景
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
    }
    
    // 显示抽卡结果
    displayDrawResults() {
        // 标题
        this.ctx.font = '24px Arial';
        this.ctx.fillStyle = '#fff';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(`抽卡结果`, this.screenWidth / 2, 50);
        
        const startY = 100;
        const itemHeight = 40;
        
        this.ctx.textAlign = 'center';
        
        this.drawResults.forEach((result, index) => {
            const y = startY + index * itemHeight;
            
            // 设置不同品质的颜色
            this.ctx.fillStyle = QUALITY_COLORS[result.quality] || '#FFFFFF';
            this.ctx.font = '18px Arial';
            
            if (result.isDuplicate) {
                this.ctx.fillText(`${result.name} [${QUALITY_NAMES[result.quality]}] (重复，获得${result.fragmentAmount}碎片)`, 
                               this.screenWidth / 2, y);
            } else {
                this.ctx.fillText(`${result.name} [${QUALITY_NAMES[result.quality]}] (新角色)`, 
                               this.screenWidth / 2, y);
            }
        });
    }
    
    drawPityProgress() {
        const { x, y, width, height, color } = this.pityProgressBar;
        const progress = this.pityCounter / this.pityThreshold;
        
        // 绘制背景
        this.ctx.fillStyle = '#ddd';
        this.ctx.fillRect(x, y, width, height);
        
        // 绘制进度
        this.ctx.fillStyle = color;
        this.ctx.fillRect(x, y, width * progress, height);
        
        // 绘制边框
        this.ctx.strokeStyle = '#000';
        this.ctx.strokeRect(x, y, width, height);
    }

    displayPityInfo() {
        this.ctx.font = '16px Arial';
        this.ctx.fillStyle = '#fff';
        this.ctx.textAlign = 'center';
        
        // 显示保底进度
        const remainingPity = this.pityThreshold - this.pityCounter;
        this.ctx.fillText(
            `距离必出神话品质还需 ${remainingPity} 抽`,
            this.screenWidth / 2,
            this.screenHeight - 60
        );
        
        // 显示当前概率提升
        const baseRate = this.probabilities[QUALITY.DIVINE];
        const boostedRate = baseRate + (this.pityCounter * 0.001); // 每抽增加0.1%出率
        this.ctx.fillText(
            `当前神话品质出率: ${(boostedRate * 100).toFixed(2)}%`,
            this.screenWidth / 2,
            this.screenHeight - 40
        );
    }
}
