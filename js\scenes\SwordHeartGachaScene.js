/**
 * 剑心抽卡场景类
 * 用于抽取剑心
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import Dialog from '../ui/Dialog';
import game from '../../game';
import { SWORD_HEART_CONFIG } from '../config/SwordHeartConfig.js';

class SwordHeartGachaScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager, resources) {
    super(ctx, screenWidth, screenHeight, sceneManager);

    // 场景资源
    this.resources = resources || {};

    // 当前选中的底部导航项
    this.selectedTabIndex = 0; // 主页对应的导航索引

    // 抽卡结果
    this.gachaResult = null;

    // 抽卡动画状态
    this.isAnimating = false;

    // 抽卡动画计时器
    this.animationTimer = 0;

    // 抽卡动画总时长（毫秒）
    this.animationDuration = 1000; // 缩短为1秒

    // 初始化UI
    this.initUI();
  }

  // 初始化UI
  initUI() {
    // 清空UI元素
    this.clearUIElements();

    // 创建返回按钮
    this.createBackButton();

    // 创建抽卡按钮
    this.createGachaButtons();
  }

  // 创建返回按钮
  createBackButton() {
    const buttonWidth = 80;
    const buttonHeight = 40;
    const margin = 10;

    this.backButton = new Button(
      this.ctx,
      margin,
      margin,
      buttonWidth,
      buttonHeight,
      '返回',
      null,
      null,
      () => {
        this.sceneManager.showScene('main');
      }
    );

    this.addUIElement(this.backButton);
  }

  // 创建抽卡按钮
  createGachaButtons() {
    const buttonWidth = 150;
    const buttonHeight = 50;
    const margin = 20;
    const centerY = this.screenHeight * 0.7;

    // 单抽按钮
    this.singleGachaButton = new Button(
      this.ctx,
      this.screenWidth / 2 - buttonWidth - margin,
      centerY,
      buttonWidth,
      buttonHeight,
      '单抽',
      null,
      '#4299e1',
      () => {
        this.performGacha(1);
      }
    );

    this.addUIElement(this.singleGachaButton);

    // 十连抽按钮
    this.tenGachaButton = new Button(
      this.ctx,
      this.screenWidth / 2 + margin,
      centerY,
      buttonWidth,
      buttonHeight,
      '十连抽',
      null,
      '#f56565',
      () => {
        this.performGacha(10);
      }
    );

    this.addUIElement(this.tenGachaButton);
  }

  // 执行抽卡
  performGacha(count) {
    // 检查剑心缘是否足够
    const player = game.gameStateManager.getPlayer();
    const swordYuan = player.resources.swordYuan || 0;
    const xianyu = player.resources.xianyu || 0;

    // 计算需要的剑心缘
    const requiredSwordYuan = count;

    // 如果剑心缘不足，检查是否可以用仙玉补齐
    if (swordYuan < requiredSwordYuan) {
      const missingSwordYuan = requiredSwordYuan - swordYuan;
      const requiredXianyu = missingSwordYuan * 100; // 1剑心缘=100仙玉

      if (xianyu < requiredXianyu) {
        // 仙玉也不足，提示充值
        this.showMessage(`剑心缘不足，需要${requiredSwordYuan}剑心缘或${requiredXianyu}仙玉，请获取更多资源`);
        return;
      }

      // 确认是否使用仙玉
      this.showConfirmDialog(
        `剑心缘不足，是否使用${requiredXianyu}仙玉补齐？`,
        () => {
          // 扣除仙玉
          game.gameStateManager.addResource('xianyu', -requiredXianyu);

          // 执行抽卡
          this.doGacha(count);
        }
      );
    } else {
      // 剑心缘足够，直接扣除
      game.gameStateManager.addResource('swordYuan', -requiredSwordYuan);

      // 执行抽卡
      this.doGacha(count);
    }
  }

  // 实际执行抽卡逻辑
  doGacha(count) {
    // 抽卡结果
    const results = [];

    // 获取所有可能的剑心
    const allSwordHearts = SWORD_HEART_CONFIG.map(config => ({
      id: config.id,
      name: config.name,
      color: config.color,
      rarity: config.rarity || 'common' // 默认为普通稀有度
    }));

    // 抽取指定数量的剑心
    for (let i = 0; i < count; i++) {
      // 随机选择一个剑心
      const randomIndex = Math.floor(Math.random() * allSwordHearts.length);
      const swordHeart = allSwordHearts[randomIndex];

      // 添加到结果中
      results.push({
        id: swordHeart.id,
        name: swordHeart.name,
        color: swordHeart.color,
        rarity: swordHeart.rarity
      });

      // 添加剑心物品到背包
      const itemId = `swordheart_${swordHeart.id}`;
      game.gameStateManager.addItem({
        id: itemId,
        name: `${swordHeart.name}`,
        type: 'swordheart',
        quality: swordHeart.rarity === 'rare' ? 'rare' : 'common',
        count: 1,
        stackable: true,
        description: `用于进阶${swordHeart.name}的材料`
      });

      // 解锁剑心
      game.gameStateManager.unlockSwordHeart(swordHeart.id);
    }

    // 保存抽卡结果
    this.gachaResult = results;

    // 开始抽卡动画
    this.isAnimating = true;
    this.animationTimer = 0;

    // 播放抽卡音效
    if (game.audioManager) {
      game.audioManager.playSound('gacha');
    }
  }

  // 显示确认对话框
  showConfirmDialog(message, onConfirm) {
    const dialogButtons = [
      {
        text: '确定',
        normalImg: null,
        pressedImg: null,
        onClick: () => {
          onConfirm();
        },
        closeDialog: true
      },
      {
        text: '取消',
        normalImg: null,
        pressedImg: null,
        onClick: null,
        closeDialog: true
      }
    ];

    const dialog = new Dialog(
      this.ctx,
      this.screenWidth,
      this.screenHeight,
      '确认',
      message,
      dialogButtons
    );

    this.addUIElement(dialog);
    dialog.show();
  }

  // 显示消息对话框
  showMessage(message) {
    const dialogButtons = [
      {
        text: '确定',
        normalImg: null,
        pressedImg: null,
        onClick: null,
        closeDialog: true
      }
    ];

    const dialog = new Dialog(
      this.ctx,
      this.screenWidth,
      this.screenHeight,
      '提示',
      message,
      dialogButtons
    );

    this.addUIElement(dialog);
    dialog.show();
  }

  // 场景显示时的回调
  onShow(params) {
    // 清空UI元素
    this.clearUIElements();

    // 初始化UI
    this.initUI();

    // 重置抽卡结果
    this.gachaResult = null;
    this.isAnimating = false;

    // 更新选中的导航项
    this.selectedTabIndex = 0;
  }

  // 场景隐藏时的回调
  onHide() {
    // 清空UI元素
    this.clearUIElements();

    // 重置抽卡结果
    this.gachaResult = null;
    this.isAnimating = false;
  }

  // 更新场景
  update(deltaTime) {
    super.update(deltaTime);

    // 更新抽卡动画
    if (this.isAnimating) {
      // 如果deltaTime未定义或为0，使用默认值16.67ms (约60fps)
      const dt = (deltaTime && deltaTime > 0) ? deltaTime : 16.67;

      this.animationTimer += dt;

      // 动画结束
      if (this.animationTimer >= this.animationDuration) {
        this.isAnimating = false;

        // 显示抽卡结果
        this.showGachaResult();
      }

      // 强制重绘
      if (this.sceneManager && typeof this.sceneManager.render === 'function') {
        this.sceneManager.render();
      }
    }
  }

  // 场景特定的更新逻辑
  updateScene() {
    // 更新抽卡动画
    if (this.isAnimating) {
      // 使用默认值16.67ms (约60fps)
      const dt = 16.67;

      this.animationTimer += dt;

      // 动画结束
      if (this.animationTimer >= this.animationDuration) {
        this.isAnimating = false;

        // 显示抽卡结果
        this.showGachaResult();
      }
    }
  }

  // 显示抽卡结果
  showGachaResult() {
    if (!this.gachaResult || this.gachaResult.length === 0) return;

    // 构建结果消息
    let message = '恭喜获得以下剑心：\n\n';

    this.gachaResult.forEach(result => {
      message += `${result.name}\n`;
    });

    // 显示结果对话框
    this.showMessage(message);

    // 重置抽卡结果
    this.gachaResult = null;
  }

  // 子类实现的绘制逻辑
  drawScene() {
    // 绘制背景
    this.drawBackground();

    // 绘制标题
    this.drawTitle();

    // 绘制资源信息
    this.drawResourceInfo();

    // 绘制抽卡说明
    this.drawGachaInfo();

    // 如果正在动画中，绘制抽卡动画
    if (this.isAnimating) {
      this.drawGachaAnimation();
    }
  }

  // 绘制背景
  drawBackground() {
    // 绘制渐变背景
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
    gradient.addColorStop(0, '#1a202c');
    gradient.addColorStop(1, '#2d3748');

    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
  }

  // 绘制标题
  drawTitle() {
    const headerHeight = 80;

    // 绘制标题背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    this.ctx.fillRect(0, 0, this.screenWidth, headerHeight);

    // 绘制标题文字
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText('剑心抽卡', this.screenWidth / 2, headerHeight / 2);
  }

  // 绘制资源信息
  drawResourceInfo() {
    const headerHeight = 80;
    const margin = 20;

    // 获取玩家资源
    const player = game.gameStateManager.getPlayer();
    const swordYuan = player.resources.swordYuan || 0;
    const xianyu = player.resources.xianyu || 0;

    // 绘制剑心缘
    this.ctx.font = '18px Arial';
    this.ctx.fillStyle = '#4299e1';
    this.ctx.textAlign = 'right';
    this.ctx.fillText(`剑心缘: ${swordYuan}`, this.screenWidth - margin, headerHeight + margin);

    // 绘制仙玉
    this.ctx.fillStyle = '#f56565';
    this.ctx.fillText(`仙玉: ${xianyu}`, this.screenWidth - margin, headerHeight + margin + 30);
  }

  // 绘制抽卡说明
  drawGachaInfo() {
    const headerHeight = 80;
    const margin = 20;

    // 绘制抽卡说明背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    this.ctx.fillRect(margin, headerHeight + margin * 3, this.screenWidth - margin * 2, 100);

    // 绘制抽卡说明文字
    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';
    this.ctx.textBaseline = 'top';
    this.ctx.fillText('抽卡说明:', margin * 2, headerHeight + margin * 3 + 20);
    this.ctx.fillText('1. 单抽需要消耗1剑心缘', margin * 2, headerHeight + margin * 3 + 50);
    this.ctx.fillText('2. 十连抽需要消耗10剑心缘', margin * 2, headerHeight + margin * 3 + 80);
    this.ctx.fillText('3. 剑心缘不足时可使用仙玉补齐（1剑心缘=100仙玉）', margin * 2, headerHeight + margin * 3 + 110);
  }

  // 绘制抽卡动画
  drawGachaAnimation() {
    // 计算动画进度（0-1）
    const progress = Math.min(this.animationTimer / this.animationDuration, 1);

    // 绘制动画背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);

    // 绘制动画文字
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';

    if (progress < 0.3) {
      // 第一阶段：显示"抽取中"
      this.ctx.fillText('抽取中...', this.screenWidth / 2, this.screenHeight / 2);
    } else if (progress < 0.6) {
      // 第二阶段：显示"剑心凝聚中"
      this.ctx.fillText('剑心凝聚中...', this.screenWidth / 2, this.screenHeight / 2);
    } else {
      // 第三阶段：显示"即将揭晓"
      this.ctx.fillText('即将揭晓...', this.screenWidth / 2, this.screenHeight / 2);
    }

    // 绘制进度条
    const progressBarWidth = this.screenWidth * 0.6;
    const progressBarHeight = 10;
    const progressBarX = (this.screenWidth - progressBarWidth) / 2;
    const progressBarY = this.screenHeight / 2 + 40;

    // 进度条背景
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
    this.ctx.fillRect(progressBarX, progressBarY, progressBarWidth, progressBarHeight);

    // 进度条前景
    this.ctx.fillStyle = '#4299e1';
    this.ctx.fillRect(progressBarX, progressBarY, progressBarWidth * progress, progressBarHeight);
  }
}

export default SwordHeartGachaScene;
