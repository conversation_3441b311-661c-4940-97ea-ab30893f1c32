/**
 * 单人战斗系统
 * 基于攻击速度的时间轴战斗系统
 */
import SingleBattleScene from './scenes/SingleBattleScene';
import BattleResultScene from './scenes/BattleResultScene';
import AppContext from '../utils/AppContext';

class SingleBattleSystem {
  constructor() {
    this.ctx = null;
    this.screenWidth = 0;
    this.screenHeight = 0;
    this.sceneManager = null;

    // 战斗相关场景
    this.battleScene = null;
    this.resultScene = null;

    // 战斗相关数据
    this.stageData = null;        // 关卡数据
    this.playerCharacter = null;  // 玩家角色
    this.enemyCharacter = null;   // 敌方角色
    this.battleResult = null;     // 战斗结果
    this.rewards = null;          // 战斗奖励

    // 战斗状态
    this.isInBattle = false;
    this.battleStartTime = 0;
    this.playerTimeline = 0;      // 玩家时间轴
    this.enemyTimeline = 0;       // 敌人时间轴
    this.skillCooldowns = {};     // 技能冷却时间

    // 战斗配置
    this.baseAttackInterval = 1000; // 基础攻击间隔（1秒）
  }

  /**
   * 初始化战斗系统
   * @param {Object} ctx 画布上下文
   * @param {number} screenWidth 屏幕宽度
   * @param {number} screenHeight 屏幕高度
   * @param {Object} sceneManager 场景管理器
   */
  init(ctx, screenWidth, screenHeight, sceneManager) {
    this.ctx = ctx;
    this.screenWidth = screenWidth;
    this.screenHeight = screenHeight;
    this.sceneManager = sceneManager;

    // 初始化战斗相关场景
    this.battleScene = new SingleBattleScene(ctx, screenWidth, screenHeight, sceneManager);
    this.resultScene = new BattleResultScene(ctx, screenWidth, screenHeight, sceneManager);

    // 注册场景到场景管理器
    sceneManager.registerScene('singleBattle', this.battleScene);
    sceneManager.registerScene('battleResult', this.resultScene);

    console.log('单人战斗系统初始化完成');
  }

  /**
   * 开始单人战斗
   * @param {Object} stageData 关卡数据
   * @param {Object} playerCharacter 玩家角色
   * @param {Function} onComplete 战斗完成回调
   */
  startBattle(stageData, playerCharacter, onComplete) {
    console.log('开始单人战斗，关卡数据:', stageData);
    console.log('玩家角色:', playerCharacter);

    // 保存战斗数据
    this.stageData = stageData;
    this.playerCharacter = playerCharacter;
    this.enemyCharacter = this.createEnemyCharacter(stageData);
    this.rewards = stageData.rewards || {};
    this.onBattleComplete = onComplete || null;

    // 初始化战斗状态
    this.initBattleState();

    // 切换到单人战斗场景
    this.sceneManager.showScene('singleBattle', {
      playerCharacter: this.playerCharacter,
      enemyCharacter: this.enemyCharacter,
      stageData: this.stageData,
      battleSystem: this,
      onBattleEnd: this.onBattleEnd.bind(this)
    });
  }

  /**
   * 创建敌方角色
   * @param {Object} stageData 关卡数据
   * @returns {Object} 敌方角色
   */
  createEnemyCharacter(stageData) {
    // 基于关卡数据创建敌方角色
    const enemy = {
      id: 'enemy_' + Date.now(),
      name: stageData.enemyName || '敌人',
      level: stageData.enemyLevel || 1,
      hp: stageData.enemyHp || 1000,
      maxHp: stageData.enemyHp || 1000,
      attack: stageData.enemyAttack || 100,
      defense: stageData.enemyDefense || 50,
      attackSpeed: stageData.enemyAttackSpeed || 0, // 攻击速度百分比

      // 敌人技能（简化版）
      skills: [
        {
          id: 'enemy_normal_attack',
          name: '普通攻击',
          type: 'normalAttack',
          damage: stageData.enemyAttack || 100,
          castTime: 500,
          cooldown: 0,
          animationType: 'melee'
        }
      ],

      // 当前状态
      currentHp: stageData.enemyHp || 1000,
      isAlive: true
    };

    return enemy;
  }

  /**
   * 初始化战斗状态
   */
  initBattleState() {
    this.isInBattle = true;
    this.battleStartTime = Date.now();
    this.playerTimeline = 0;
    this.enemyTimeline = 0;
    this.skillCooldowns = {};

    // 初始化角色状态
    if (this.playerCharacter) {
      const attributes = this.playerCharacter.getAttributes();
      this.playerCharacter.currentHp = attributes.hp;
      this.playerCharacter.maxHp = attributes.hp;
      this.playerCharacter.isAlive = true;
    }

    if (this.enemyCharacter) {
      this.enemyCharacter.currentHp = this.enemyCharacter.maxHp;
      this.enemyCharacter.isAlive = true;
    }
  }

  /**
   * 计算攻击间隔
   * @param {Object} character 角色
   * @returns {number} 攻击间隔（毫秒）
   */
  calculateAttackInterval(character) {
    const attackSpeed = character.attackSpeed || 0;

    // 攻击速度公式：基础间隔 / (1 + 攻击速度百分比/100)
    const interval = this.baseAttackInterval / (1 + attackSpeed / 100);

    return Math.max(100, interval); // 最小间隔100ms
  }

  /**
   * 更新战斗时间轴
   * @param {number} deltaTime 时间差（毫秒）
   */
  updateTimeline(deltaTime) {
    if (!this.isInBattle) return;

    // 更新玩家时间轴
    if (this.playerCharacter && this.playerCharacter.isAlive) {
      this.playerTimeline += deltaTime;
    }

    // 更新敌人时间轴
    if (this.enemyCharacter && this.enemyCharacter.isAlive) {
      this.enemyTimeline += deltaTime;
    }

    // 更新技能冷却
    this.updateSkillCooldowns(deltaTime);

    // 检查是否可以执行普通攻击
    this.checkAutoAttacks();
  }

  /**
   * 更新技能冷却时间
   * @param {number} deltaTime 时间差（毫秒）
   */
  updateSkillCooldowns(deltaTime) {
    Object.keys(this.skillCooldowns).forEach(skillId => {
      this.skillCooldowns[skillId] = Math.max(0, this.skillCooldowns[skillId] - deltaTime);
    });
  }

  /**
   * 检查自动攻击
   */
  checkAutoAttacks() {
    // 检查玩家普通攻击
    if (this.playerCharacter && this.playerCharacter.isAlive) {
      const playerInterval = this.calculateAttackInterval(this.playerCharacter);
      if (this.playerTimeline >= playerInterval) {
        this.executePlayerNormalAttack();
        this.playerTimeline = 0;
      }
    }

    // 检查敌人普通攻击
    if (this.enemyCharacter && this.enemyCharacter.isAlive) {
      const enemyInterval = this.calculateAttackInterval(this.enemyCharacter);
      if (this.enemyTimeline >= enemyInterval) {
        this.executeEnemyNormalAttack();
        this.enemyTimeline = 0;
      }
    }
  }

  /**
   * 执行玩家普通攻击
   */
  executePlayerNormalAttack() {
    if (!this.playerCharacter || !this.enemyCharacter || !this.enemyCharacter.isAlive) return;

    // 获取玩家装备的普通攻击技能
    const normalAttackSkill = this.playerCharacter.getSkillInSlot(0);
    if (normalAttackSkill) {
      this.executeSkill(this.playerCharacter, this.enemyCharacter, normalAttackSkill);
    } else {
      // 使用默认普通攻击
      this.executeDefaultAttack(this.playerCharacter, this.enemyCharacter);
    }
  }

  /**
   * 执行敌人普通攻击
   */
  executeEnemyNormalAttack() {
    if (!this.enemyCharacter || !this.playerCharacter || !this.playerCharacter.isAlive) return;

    // 敌人使用默认普通攻击
    this.executeDefaultAttack(this.enemyCharacter, this.playerCharacter);
  }

  /**
   * 执行默认攻击
   * @param {Object} attacker 攻击者
   * @param {Object} target 目标
   */
  executeDefaultAttack(attacker, target) {
    const damage = this.calculateDamage(attacker, target);
    this.applyDamage(target, damage);

    // 通知战斗场景播放攻击动画
    if (this.battleScene) {
      this.battleScene.playAttackAnimation(attacker, target, damage);
    }

    console.log(`${attacker.name} 对 ${target.name} 造成了 ${damage} 点伤害`);
  }

  /**
   * 计算伤害
   * @param {Object} attacker 攻击者
   * @param {Object} target 目标
   * @returns {number} 伤害值
   */
  calculateDamage(attacker, target) {
    const attackPower = attacker.attack || 100;
    const defense = target.defense || 0;

    // 简单的伤害计算公式
    const baseDamage = Math.max(1, attackPower - defense * 0.5);

    // 添加随机性
    const randomFactor = 0.8 + Math.random() * 0.4; // 80%-120%

    return Math.floor(baseDamage * randomFactor);
  }

  /**
   * 执行技能
   * @param {Object} caster 施法者
   * @param {Object} target 目标
   * @param {Object} skill 技能
   */
  executeSkill(caster, target, skill) {
    if (!skill || !target || !target.isAlive) return;

    // 计算技能伤害
    let damage = skill.damage || 0;

    // 如果技能没有设置伤害，使用角色攻击力
    if (damage === 0) {
      damage = caster.attack || 100;
    }

    // 应用技能伤害倍率
    if (skill.type === 'activeSkill') {
      damage = Math.floor(damage * 1.2); // 主动技能伤害加成
    }

    // 计算最终伤害
    const finalDamage = this.calculateSkillDamage(caster, target, damage);

    // 应用伤害
    this.applyDamage(target, finalDamage);

    // 通知战斗场景播放技能动画
    if (this.battleScene) {
      this.battleScene.playSkillAnimation(caster, target, skill, finalDamage);
    }

    console.log(`${caster.name} 使用 ${skill.name} 对 ${target.name} 造成了 ${finalDamage} 点伤害`);
  }

  /**
   * 计算技能伤害
   * @param {Object} caster 施法者
   * @param {Object} target 目标
   * @param {number} baseDamage 基础伤害
   * @returns {number} 最终伤害
   */
  calculateSkillDamage(caster, target, baseDamage) {
    const defense = target.defense || 0;

    // 技能伤害计算（比普通攻击更高）
    const damage = Math.max(1, baseDamage - defense * 0.3);

    // 添加随机性
    const randomFactor = 0.9 + Math.random() * 0.2; // 90%-110%

    return Math.floor(damage * randomFactor);
  }

  /**
   * 应用伤害
   * @param {Object} target 目标
   * @param {number} damage 伤害值
   */
  applyDamage(target, damage) {
    target.currentHp = Math.max(0, target.currentHp - damage);

    if (target.currentHp <= 0) {
      target.isAlive = false;
      this.checkBattleEnd();
    }
  }

  /**
   * 检查战斗是否结束
   */
  checkBattleEnd() {
    if (!this.playerCharacter.isAlive) {
      // 玩家失败
      this.onBattleEnd('defeat');
    } else if (!this.enemyCharacter.isAlive) {
      // 玩家胜利
      this.onBattleEnd('victory');
    }
  }

  /**
   * 战斗结束回调
   * @param {string} result 战斗结果
   */
  onBattleEnd(result) {
    console.log('单人战斗结束，结果:', result);

    this.isInBattle = false;
    this.battleResult = result;

    if (result === 'victory') {
      // 战斗胜利，显示结算场景
      this.showBattleResult();
    } else {
      // 战斗失败，直接返回
      if (this.onBattleComplete) {
        this.onBattleComplete({
          victory: false,
          rewards: {}
        });
      }
    }
  }

  /**
   * 显示战斗结果
   */
  showBattleResult() {
    // 确保奖励数据存在
    if (!this.rewards || Object.keys(this.rewards).length === 0) {
      this.rewards = {
        exp: Math.floor(50 + Math.random() * 20),
        lingshi: Math.floor(100 + Math.random() * 50),
        lianlidian: Math.floor(10 + Math.random() * 10)
      };
    }

    console.log('战斗胜利奖励:', this.rewards);

    // 显示结算场景
    this.sceneManager.showScene('battleResult', {
      result: { victory: true },
      rewards: this.rewards,
      onComplete: this.onResultComplete.bind(this)
    });
  }

  /**
   * 结算完成回调
   */
  onResultComplete() {
    console.log('单人战斗结算完成');

    if (this.onBattleComplete) {
      this.onBattleComplete({
        victory: true,
        rewards: this.rewards
      });
    }
  }

  /**
   * 清理战斗数据
   */
  clear() {
    this.stageData = null;
    this.playerCharacter = null;
    this.enemyCharacter = null;
    this.battleResult = null;
    this.rewards = null;
    this.onBattleComplete = null;
    this.isInBattle = false;
    this.skillCooldowns = {};
  }
}

// 导出单人战斗系统单例
const singleBattleSystem = new SingleBattleSystem();
export default singleBattleSystem;
