/**
 * 挂机战斗场景
 * 60秒限时战斗，击杀敌人数量决定挂机收益
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import singleBattleSystem from '../battle/SingleBattleSystem';
import game from '../../game';

class IdleBattleScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager) {
    super(ctx, screenWidth, screenHeight, sceneManager);

    // 战斗数据
    this.playerCharacter = null;
    this.currentEnemy = null;
    this.enemyCount = 0; // 已击杀敌人数量
    this.battleTimeLimit = 60000; // 60秒限制
    this.battleStartTime = 0;
    this.battleTimeRemaining = 0;
    this.isActive = false;

    // 地点配置
    this.locationConfig = null;

    // UI元素
    this.skillButtons = [];
    this.exitButton = null;

    // 动画相关
    this.animations = [];
    this.lastUpdateTime = 0;

    // 战斗UI配置
    this.playerPosition = { x: screenWidth * 0.2, y: screenHeight * 0.6 };
    this.enemyPosition = { x: screenWidth * 0.8, y: screenHeight * 0.6 };
    this.characterSize = 80;
  }

  /**
   * 场景显示时的回调
   */
  onShow(params) {
    try {
      console.log("IdleBattleScene onShow", params);
      this.visible = true;

      // 获取玩家角色和地点配置
      this.playerCharacter = params.playerCharacter;
      this.locationConfig = params.locationConfig;

      // 初始化战斗状态
      this.initBattle();

      // 初始化UI
      this.initUI();

      // 开始战斗循环
      this.lastUpdateTime = Date.now();
      this.startBattleLoop();

      console.log('挂机战斗场景初始化完成');
    } catch (error) {
      console.error("IdleBattleScene.onShow 出错:", error);
    }
  }

  /**
   * 初始化战斗
   */
  initBattle() {
    this.enemyCount = 0;
    this.battleStartTime = Date.now();
    this.battleTimeRemaining = this.battleTimeLimit;
    this.isActive = true;

    // 初始化玩家状态
    if (this.playerCharacter) {
      const attributes = this.playerCharacter.getAttributes();
      this.playerCharacter.currentHp = attributes.hp;
      this.playerCharacter.maxHp = attributes.hp;
      this.playerCharacter.isAlive = true;
    }

    // 生成第一个敌人
    this.spawnNextEnemy();
  }

  /**
   * 生成下一个敌人
   */
  spawnNextEnemy() {
    this.enemyCount++;

    if (!this.locationConfig) {
      console.error('地点配置缺失');
      return;
    }

    let enemyType = 'normal';
    let enemyConfig = this.locationConfig.normalEnemy;
    let name = enemyConfig.name;

    if (this.enemyCount % 30 === 0) {
      // 第30个敌人是Boss
      enemyType = 'boss';
      enemyConfig = this.locationConfig.bossEnemy;
      name = enemyConfig.name;
    } else if (this.enemyCount % 10 === 0) {
      // 第10个敌人是精英
      enemyType = 'elite';
      enemyConfig = this.locationConfig.eliteEnemy;
      name = enemyConfig.name;
    }

    // 根据玩家等级调整敌人属性
    const playerLevel = this.playerCharacter.level || 1;
    const levelMultiplier = 1 + (playerLevel - 1) * 0.1;

    // 计算敌人属性
    let hp, attack, defense, attackSpeed;

    if (enemyType === 'normal') {
      hp = Math.floor(enemyConfig.hpBase * levelMultiplier);
      attack = Math.floor(enemyConfig.attackBase * levelMultiplier);
      defense = Math.floor(enemyConfig.defenseBase * levelMultiplier);
      attackSpeed = enemyConfig.attackSpeed || 0;
    } else {
      // 精英和Boss使用倍率计算
      const baseHp = this.locationConfig.normalEnemy.hpBase * levelMultiplier;
      const baseAttack = this.locationConfig.normalEnemy.attackBase * levelMultiplier;
      const baseDefense = this.locationConfig.normalEnemy.defenseBase * levelMultiplier;

      hp = Math.floor(baseHp * enemyConfig.hpMultiplier);
      attack = Math.floor(baseAttack * enemyConfig.attackMultiplier);
      defense = Math.floor(baseDefense * enemyConfig.defenseMultiplier);
      attackSpeed = enemyConfig.attackSpeed || 0;
    }

    this.currentEnemy = {
      id: 'idle_enemy_' + this.enemyCount,
      name: name,
      type: enemyType,
      level: playerLevel,
      hp: hp,
      maxHp: hp,
      attack: attack,
      defense: defense,
      attackSpeed: attackSpeed,
      skills: enemyConfig.skills || [],

      // 当前状态
      currentHp: hp,
      isAlive: true
    };

    console.log(`生成敌人: ${this.currentEnemy.name}, 血量: ${this.currentEnemy.hp}, 类型: ${enemyType}`);
  }

  /**
   * 初始化UI
   */
  initUI() {
    this.skillButtons = [];

    // 创建技能按钮
    this.createSkillButtons();

    // 创建退出按钮
    this.exitButton = new Button(
      this.ctx,
      this.screenWidth - 80,
      20,
      60,
      30,
      '退出',
      null,
      null,
      () => {
        this.endBattle();
      }
    );

    this.addUIElement(this.exitButton);
  }

  /**
   * 创建技能按钮
   */
  createSkillButtons() {
    if (!this.playerCharacter) return;

    const buttonSize = 60;
    const buttonSpacing = 10;
    const startX = 20;
    const startY = this.screenHeight - buttonSize - 20;

    // 创建主动技能按钮（槽位1-5）
    for (let i = 1; i <= 5; i++) {
      const skill = this.playerCharacter.getSkillInSlot(i);
      if (skill) {
        const button = new Button(
          this.ctx,
          startX + (i - 1) * (buttonSize + buttonSpacing),
          startY,
          buttonSize,
          buttonSize,
          skill.name.charAt(0),
          null,
          null,
          () => {
            this.useSkill(skill);
          }
        );

        // 设置技能按钮样式
        button.backgroundColor = '#4ECDC4';
        button.textColor = '#FFFFFF';
        button.fontSize = '18px';

        this.skillButtons.push({
          button: button,
          skill: skill,
          slotIndex: i
        });
      }
    }

    // 添加按钮到UI元素列表
    this.skillButtons.forEach(skillButton => {
      this.addUIElement(skillButton.button);
    });
  }

  /**
   * 使用技能
   */
  useSkill(skill) {
    if (!this.isActive || !this.currentEnemy || !this.currentEnemy.isAlive) return;

    // 检查技能冷却
    const skillId = skill.id;
    const cooldownRemaining = this.skillCooldowns[skillId] || 0;

    if (cooldownRemaining > 0) {
      return;
    }

    // 执行技能
    this.executeSkill(this.playerCharacter, this.currentEnemy, skill);

    // 设置技能冷却
    this.skillCooldowns[skillId] = skill.cooldown || 0;
  }

  /**
   * 开始战斗循环
   */
  startBattleLoop() {
    // 初始化技能冷却
    this.skillCooldowns = {};
    this.playerTimeline = 0;
    this.enemyTimeline = 0;

    const update = () => {
      if (!this.visible || !this.isActive) {
        return;
      }

      const currentTime = Date.now();
      const deltaTime = currentTime - this.lastUpdateTime;
      this.lastUpdateTime = currentTime;

      // 更新战斗时间
      this.updateBattleTime(deltaTime);

      // 更新战斗逻辑
      this.updateBattle(deltaTime);

      // 更新动画
      this.updateAnimations(deltaTime);

      // 继续循环
      if (this.isActive) {
        requestAnimationFrame(update);
      }
    };

    requestAnimationFrame(update);
  }

  /**
   * 更新战斗时间
   */
  updateBattleTime(deltaTime) {
    this.battleTimeRemaining = Math.max(0, this.battleTimeRemaining - deltaTime);

    if (this.battleTimeRemaining <= 0) {
      this.endBattle();
    }
  }

  /**
   * 更新战斗逻辑
   */
  updateBattle(deltaTime) {
    if (!this.currentEnemy || !this.currentEnemy.isAlive) {
      // 当前敌人已死亡，生成下一个
      this.spawnNextEnemy();
      return;
    }

    // 更新时间轴
    this.playerTimeline += deltaTime;
    this.enemyTimeline += deltaTime;

    // 更新技能冷却
    this.updateSkillCooldowns(deltaTime);

    // 检查自动攻击
    this.checkAutoAttacks();
  }

  /**
   * 更新技能冷却时间
   */
  updateSkillCooldowns(deltaTime) {
    Object.keys(this.skillCooldowns).forEach(skillId => {
      this.skillCooldowns[skillId] = Math.max(0, this.skillCooldowns[skillId] - deltaTime);
    });
  }

  /**
   * 检查自动攻击
   */
  checkAutoAttacks() {
    const baseAttackInterval = 1000; // 1秒基础攻击间隔

    // 检查玩家普通攻击
    if (this.playerCharacter && this.playerCharacter.isAlive) {
      const playerInterval = this.calculateAttackInterval(this.playerCharacter, baseAttackInterval);
      if (this.playerTimeline >= playerInterval) {
        this.executePlayerNormalAttack();
        this.playerTimeline = 0;
      }
    }

    // 检查敌人普通攻击
    if (this.currentEnemy && this.currentEnemy.isAlive) {
      const enemyInterval = this.calculateAttackInterval(this.currentEnemy, baseAttackInterval);
      if (this.enemyTimeline >= enemyInterval) {
        this.executeEnemyNormalAttack();
        this.enemyTimeline = 0;
      }
    }
  }

  /**
   * 计算攻击间隔
   */
  calculateAttackInterval(character, baseInterval) {
    let attackSpeed = 0;

    if (character.getAttributes) {
      const attributes = character.getAttributes();
      attackSpeed = attributes.attackSpeed || 0;
    } else {
      attackSpeed = character.attackSpeed || 0;
    }

    const interval = baseInterval / (1 + attackSpeed / 100);
    return Math.max(100, interval);
  }

  /**
   * 执行玩家普通攻击
   */
  executePlayerNormalAttack() {
    if (!this.currentEnemy || !this.currentEnemy.isAlive) return;

    const normalAttackSkill = this.playerCharacter.getSkillInSlot(0);
    if (normalAttackSkill) {
      this.executeSkill(this.playerCharacter, this.currentEnemy, normalAttackSkill);
    } else {
      this.executeDefaultAttack(this.playerCharacter, this.currentEnemy);
    }
  }

  /**
   * 执行敌人普通攻击
   */
  executeEnemyNormalAttack() {
    if (!this.playerCharacter || !this.playerCharacter.isAlive) return;

    this.executeDefaultAttack(this.currentEnemy, this.playerCharacter);
  }

  /**
   * 执行默认攻击
   */
  executeDefaultAttack(attacker, target) {
    const damage = this.calculateDamage(attacker, target);
    this.applyDamage(target, damage);

    this.playAttackAnimation(attacker, target, damage);
    console.log(`${attacker.name} 对 ${target.name} 造成了 ${damage} 点伤害`);
  }

  /**
   * 执行技能
   */
  executeSkill(caster, target, skill) {
    if (!skill || !target || !target.isAlive) return;

    let damage = skill.damage || 0;

    if (damage === 0) {
      if (caster.getAttributes) {
        const attributes = caster.getAttributes();
        damage = attributes.attack || 100;
      } else {
        damage = caster.attack || 100;
      }
    }

    if (skill.type === 'activeSkill') {
      damage = Math.floor(damage * 1.2);
    }

    const finalDamage = this.calculateSkillDamage(caster, target, damage);
    this.applyDamage(target, finalDamage);

    this.playSkillAnimation(caster, target, skill, finalDamage);
    console.log(`${caster.name} 使用 ${skill.name} 对 ${target.name} 造成了 ${finalDamage} 点伤害`);
  }

  /**
   * 计算伤害
   */
  calculateDamage(attacker, target) {
    let attackPower = 100;
    if (attacker.getAttributes) {
      const attributes = attacker.getAttributes();
      attackPower = attributes.attack || 100;
    } else {
      attackPower = attacker.attack || 100;
    }

    let defense = 0;
    if (target.getAttributes) {
      const attributes = target.getAttributes();
      defense = attributes.defense || 0;
    } else {
      defense = target.defense || 0;
    }

    const baseDamage = Math.max(1, attackPower - defense * 0.5);
    const randomFactor = 0.8 + Math.random() * 0.4;

    return Math.floor(baseDamage * randomFactor);
  }

  /**
   * 计算技能伤害
   */
  calculateSkillDamage(caster, target, baseDamage) {
    let defense = 0;
    if (target.getAttributes) {
      const attributes = target.getAttributes();
      defense = attributes.defense || 0;
    } else {
      defense = target.defense || 0;
    }

    const damage = Math.max(1, baseDamage - defense * 0.3);
    const randomFactor = 0.9 + Math.random() * 0.2;

    return Math.floor(damage * randomFactor);
  }

  /**
   * 应用伤害
   */
  applyDamage(target, damage) {
    target.currentHp = Math.max(0, target.currentHp - damage);

    if (target.currentHp <= 0) {
      target.isAlive = false;

      if (target === this.playerCharacter) {
        // 玩家死亡，结束战斗
        this.endBattle();
      }
      // 敌人死亡会在下一帧生成新敌人
    }
  }

  /**
   * 结束战斗
   */
  endBattle() {
    this.isActive = false;

    // 计算奖励
    const rewards = this.calculateRewards();

    console.log(`挂机战斗结束，击杀敌人数量: ${this.enemyCount - 1}, 奖励:`, rewards);

    // 发放奖励
    this.grantRewards(rewards);

    // 返回挂机游历页面
    this.sceneManager.showScene('idle');
  }

  /**
   * 计算奖励
   */
  calculateRewards() {
    const killedEnemies = this.enemyCount - 1; // 减去当前未击杀的敌人

    if (!this.locationConfig) {
      console.error('地点配置缺失，使用默认奖励');
      return {
        lingshi: killedEnemies * 10,
        herbs: killedEnemies,
        functionPoints: killedEnemies * 2,
        lianlidian: killedEnemies
      };
    }

    // 使用地点的基础奖励
    const baseRewards = this.locationConfig.baseRewards;
    const killRewards = this.locationConfig.killRewards;

    // 计算击杀奖励倍率
    let totalMultiplier = 0;

    // 计算普通怪击杀奖励
    const normalKills = killedEnemies - Math.floor(killedEnemies / 10) - Math.floor(killedEnemies / 30);
    totalMultiplier += normalKills * killRewards.normal;

    // 计算精英怪击杀奖励
    const eliteKills = Math.floor(killedEnemies / 10) - Math.floor(killedEnemies / 30);
    totalMultiplier += eliteKills * killRewards.elite;

    // 计算Boss击杀奖励
    const bossKills = Math.floor(killedEnemies / 30);
    totalMultiplier += bossKills * killRewards.boss;

    // 计算最终奖励
    const rewards = {};
    Object.keys(baseRewards).forEach(key => {
      rewards[key] = Math.floor(baseRewards[key] * totalMultiplier);
    });

    console.log(`击杀统计: 普通${normalKills}, 精英${eliteKills}, Boss${bossKills}, 总倍率${totalMultiplier}`);

    return rewards;
  }

  /**
   * 发放奖励
   */
  grantRewards(rewards) {
    console.log('发放挂机战斗奖励:', rewards);

    // 获取玩家数据
    const playerData = game.gameStateManager.getPlayer();
    const resources = playerData.resources || {};

    // 更新资源
    Object.keys(rewards).forEach(resourceType => {
      const amount = rewards[resourceType];
      if (amount > 0) {
        if (resourceType === 'herbs') {
          // 药材需要添加到背包
          this.addHerbItem(amount);
        } else if (resourceType === 'beastMaterial1') {
          // 一阶兽材添加到背包
          this.addBeastMaterialItem(1, amount);
        } else if (resourceType === 'beastMaterial2') {
          // 二阶兽材添加到背包
          this.addBeastMaterialItem(2, amount);
        } else {
          // 其他资源直接添加
          resources[resourceType] = (resources[resourceType] || 0) + amount;
        }
      }
    });

    // 更新玩家数据
    game.gameStateManager.setPlayer({
      ...playerData,
      resources: resources
    });

    game.gameStateManager.saveGameState();

    // 显示奖励提示
    this.showRewardToast(rewards);
  }

  /**
   * 添加药材到背包
   */
  addHerbItem(count) {
    if (!count || count <= 0) return;

    const items = game.gameStateManager.getItems();
    const existingHerb = items.find(item => item.name === '一阶药材');

    if (existingHerb) {
      existingHerb.count += count;
      game.gameStateManager.updateItem(existingHerb.id, existingHerb);
    } else {
      const newHerb = {
        id: Date.now(),
        name: '一阶药材',
        type: 'material',
        quality: 1,
        count: count,
        description: '初级炼丹材料，可用于炼制一阶丹药'
      };
      game.gameStateManager.addItem(newHerb);
    }
  }

  /**
   * 添加兽材到背包
   */
  addBeastMaterialItem(tier, count) {
    if (!count || count <= 0) return;

    const items = game.gameStateManager.getItems();
    const itemName = tier === 1 ? '一阶兽材' : '二阶兽材';
    const existingMaterial = items.find(item => item.name === itemName);

    if (existingMaterial) {
      existingMaterial.count += count;
      game.gameStateManager.updateItem(existingMaterial.id, existingMaterial);
    } else {
      const newMaterial = {
        id: `beast_material_${tier}_${Date.now()}`,
        name: itemName,
        type: 'material',
        quality: tier,
        count: count,
        tier: tier,
        description: tier === 1 ?
          '初级锻造材料，可用于锻造一阶装备' :
          '中级锻造材料，可用于锻造二阶装备'
      };
      game.gameStateManager.addItem(newMaterial);
    }
  }

  /**
   * 显示奖励提示
   */
  showRewardToast(rewards) {
    const rewardTexts = [];

    Object.keys(rewards).forEach(key => {
      const amount = rewards[key];
      if (amount > 0) {
        let name = key;
        switch (key) {
          case 'lingshi': name = '灵石'; break;
          case 'herbs': name = '药材'; break;
          case 'functionPoints': name = '功法点'; break;
          case 'lianlidian': name = '历练点'; break;
          case 'beastMaterial1': name = '一阶兽材'; break;
          case 'beastMaterial2': name = '二阶兽材'; break;
        }
        rewardTexts.push(`${name}+${amount}`);
      }
    });

    if (rewardTexts.length > 0) {
      wx.showToast({
        title: `挂机战斗奖励: ${rewardTexts.join(', ')}`,
        icon: 'success',
        duration: 3000
      });
    }
  }

  /**
   * 更新动画
   */
  updateAnimations(deltaTime) {
    this.animations = this.animations.filter(animation => {
      animation.update(deltaTime);
      return !animation.isFinished();
    });
  }

  /**
   * 播放攻击动画
   */
  playAttackAnimation(attacker, target, damage) {
    const animation = {
      type: 'attack',
      attacker: attacker,
      target: target,
      damage: damage,
      duration: 1000,
      elapsed: 0,

      update: function(deltaTime) {
        this.elapsed += deltaTime;
      },

      isFinished: function() {
        return this.elapsed >= this.duration;
      }
    };

    this.animations.push(animation);
  }

  /**
   * 播放技能动画
   */
  playSkillAnimation(caster, target, skill, damage) {
    const animation = {
      type: 'skill',
      caster: caster,
      target: target,
      skill: skill,
      damage: damage,
      duration: 1500,
      elapsed: 0,

      update: function(deltaTime) {
        this.elapsed += deltaTime;
      },

      isFinished: function() {
        return this.elapsed >= this.duration;
      }
    };

    this.animations.push(animation);
  }

  /**
   * 处理触摸事件
   */
  handleTouchStart(x, y) {
    console.log(`IdleBattleScene handleTouchStart: ${x},${y}`);

    // 检查退出按钮
    if (this.exitButton && this.exitButton.isPointInside(x, y)) {
      this.exitButton.onClick();
      return true;
    }

    // 检查技能按钮点击
    for (const skillButton of this.skillButtons) {
      if (skillButton.button.isPointInside(x, y)) {
        skillButton.button.onClick();
        return true;
      }
    }

    return false;
  }

  /**
   * 绘制场景
   */
  drawScene() {
    // 绘制背景
    this.drawBackground();

    // 绘制角色
    this.drawCharacters();

    // 绘制UI
    this.drawUI();

    // 绘制动画
    this.drawAnimations();
  }

  /**
   * 绘制背景
   */
  drawBackground() {
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
    gradient.addColorStop(0, 'rgba(135, 206, 235, 0.8)');
    gradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.3)');
    gradient.addColorStop(1, 'rgba(34, 139, 34, 0.8)');

    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);

    // 绘制地面线
    this.ctx.strokeStyle = 'rgba(139, 69, 19, 0.6)';
    this.ctx.lineWidth = 3;
    this.ctx.beginPath();
    this.ctx.moveTo(0, this.screenHeight * 0.8);
    this.ctx.lineTo(this.screenWidth, this.screenHeight * 0.8);
    this.ctx.stroke();
  }

  /**
   * 绘制角色
   */
  drawCharacters() {
    // 绘制玩家角色
    if (this.playerCharacter) {
      this.drawCharacter(this.playerCharacter, this.playerPosition, true);
    }

    // 绘制当前敌人
    if (this.currentEnemy) {
      this.drawCharacter(this.currentEnemy, this.enemyPosition, false);
    }
  }

  /**
   * 绘制单个角色
   */
  drawCharacter(character, position, isPlayer) {
    const x = position.x;
    const y = position.y;
    const size = this.characterSize;

    // 根据敌人类型设置颜色
    let color = isPlayer ? '#4CAF50' : '#F44336';
    if (!isPlayer && character.type) {
      switch (character.type) {
        case 'elite':
          color = '#FF9800'; // 橙色
          break;
        case 'boss':
          color = '#9C27B0'; // 紫色
          break;
      }
    }

    // 绘制角色圆形
    this.ctx.fillStyle = color;
    this.ctx.beginPath();
    this.ctx.arc(x, y, size / 2, 0, Math.PI * 2);
    this.ctx.fill();

    // 绘制角色边框
    this.ctx.strokeStyle = '#FFFFFF';
    this.ctx.lineWidth = 3;
    this.ctx.stroke();

    // 绘制角色名称
    this.ctx.font = 'bold 14px Arial';
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(character.name, x, y - size / 2 - 10);

    // 绘制血条
    this.drawHealthBar(character, x, y + size / 2 + 10);
  }

  /**
   * 绘制血条
   */
  drawHealthBar(character, x, y) {
    const barWidth = 100;
    const barHeight = 8;
    const hpPercentage = character.currentHp / character.maxHp;

    // 绘制血条背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    this.ctx.fillRect(x - barWidth / 2, y, barWidth, barHeight);

    // 绘制血条
    this.ctx.fillStyle = hpPercentage > 0.5 ? '#4CAF50' : hpPercentage > 0.2 ? '#FF9800' : '#F44336';
    this.ctx.fillRect(x - barWidth / 2, y, barWidth * hpPercentage, barHeight);

    // 绘制血条边框
    this.ctx.strokeStyle = '#FFFFFF';
    this.ctx.lineWidth = 1;
    this.ctx.strokeRect(x - barWidth / 2, y, barWidth, barHeight);

    // 绘制血量文字
    this.ctx.font = '10px Arial';
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(`${character.currentHp}/${character.maxHp}`, x, y + barHeight + 12);
  }

  /**
   * 绘制UI
   */
  drawUI() {
    // 绘制顶部信息栏
    this.drawTopInfo();

    // 绘制技能面板
    this.drawSkillPanel();

    // 绘制技能按钮
    this.drawSkillButtons();

    // 绘制退出按钮
    if (this.exitButton) {
      this.exitButton.render();
    }
  }

  /**
   * 绘制顶部信息
   */
  drawTopInfo() {
    const panelHeight = 80;

    // 绘制信息面板背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, 0, this.screenWidth, panelHeight);

    // 绘制击杀数量
    this.ctx.font = 'bold 20px Arial';
    this.ctx.fillStyle = '#FFD700';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`击杀: ${this.enemyCount - 1}`, 20, 30);

    // 绘制剩余时间
    const timeRemaining = Math.ceil(this.battleTimeRemaining / 1000);
    this.ctx.fillStyle = timeRemaining <= 10 ? '#FF4444' : '#FFFFFF';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(`剩余时间: ${timeRemaining}秒`, this.screenWidth / 2, 30);

    // 绘制当前敌人信息
    if (this.currentEnemy) {
      let enemyInfo = `第${this.enemyCount}个敌人`;
      if (this.currentEnemy.type === 'elite') {
        enemyInfo += ' (精英)';
      } else if (this.currentEnemy.type === 'boss') {
        enemyInfo += ' (Boss)';
      }

      this.ctx.font = '16px Arial';
      this.ctx.fillStyle = '#FFFFFF';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(enemyInfo, this.screenWidth / 2, 55);
    }
  }

  /**
   * 绘制技能面板
   */
  drawSkillPanel() {
    const panelHeight = 100;
    const panelY = this.screenHeight - panelHeight;

    // 绘制面板背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, panelY, this.screenWidth, panelHeight);

    // 绘制面板标题
    this.ctx.font = 'bold 16px Arial';
    this.ctx.fillStyle = '#FFD700';
    this.ctx.textAlign = 'left';
    this.ctx.fillText('技能', 20, panelY + 25);
  }

  /**
   * 绘制技能按钮
   */
  drawSkillButtons() {
    this.skillButtons.forEach(skillButton => {
      const button = skillButton.button;
      const skill = skillButton.skill;

      // 检查技能冷却
      const cooldownRemaining = this.skillCooldowns[skill.id] || 0;
      const isOnCooldown = cooldownRemaining > 0;

      // 设置按钮状态
      if (isOnCooldown) {
        button.backgroundColor = '#666666';
        button.textColor = '#CCCCCC';
      } else {
        button.backgroundColor = '#4ECDC4';
        button.textColor = '#FFFFFF';
      }

      // 绘制按钮
      button.render();

      // 绘制冷却时间
      if (isOnCooldown) {
        const cooldownSeconds = Math.ceil(cooldownRemaining / 1000);
        this.ctx.font = 'bold 12px Arial';
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(
          cooldownSeconds.toString(),
          button.x + button.width / 2,
          button.y + button.height / 2 + 20
        );
      }
    });
  }

  /**
   * 绘制动画
   */
  drawAnimations() {
    this.animations.forEach(animation => {
      if (animation.type === 'attack') {
        this.drawAttackAnimation(animation);
      } else if (animation.type === 'skill') {
        this.drawSkillAnimation(animation);
      }
    });
  }

  /**
   * 绘制攻击动画
   */
  drawAttackAnimation(animation) {
    const progress = animation.elapsed / animation.duration;

    if (animation.target) {
      const targetPos = animation.target === this.playerCharacter ? this.playerPosition : this.enemyPosition;
      const y = targetPos.y - 50 - progress * 30;
      const alpha = 1 - progress;

      this.ctx.save();
      this.ctx.globalAlpha = alpha;
      this.ctx.font = 'bold 24px Arial';
      this.ctx.fillStyle = '#FF4444';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(`-${animation.damage}`, targetPos.x, y);
      this.ctx.restore();
    }
  }

  /**
   * 绘制技能动画
   */
  drawSkillAnimation(animation) {
    const progress = animation.elapsed / animation.duration;

    if (animation.caster && animation.target) {
      const casterPos = animation.caster === this.playerCharacter ? this.playerPosition : this.enemyPosition;
      const targetPos = animation.target === this.playerCharacter ? this.playerPosition : this.enemyPosition;

      // 绘制技能轨迹
      if (animation.skill.animationType === 'ranged' && progress < 0.5) {
        const trajectoryProgress = progress * 2;
        const x = casterPos.x + (targetPos.x - casterPos.x) * trajectoryProgress;
        const y = casterPos.y + (targetPos.y - casterPos.y) * trajectoryProgress - Math.sin(trajectoryProgress * Math.PI) * 20;

        this.ctx.save();
        this.ctx.fillStyle = '#FFD700';
        this.ctx.beginPath();
        this.ctx.arc(x, y, 8, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.restore();
      }

      // 绘制技能伤害数字
      if (progress >= 0.5) {
        const damageProgress = (progress - 0.5) * 2;
        const y = targetPos.y - 50 - damageProgress * 40;
        const alpha = 1 - damageProgress;

        this.ctx.save();
        this.ctx.globalAlpha = alpha;
        this.ctx.font = 'bold 28px Arial';
        this.ctx.fillStyle = '#FF6600';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(`-${animation.damage}`, targetPos.x, y);

        this.ctx.font = 'bold 16px Arial';
        this.ctx.fillStyle = '#FFD700';
        this.ctx.fillText(animation.skill.name, targetPos.x, y - 35);
        this.ctx.restore();
      }
    }
  }
}

export default IdleBattleScene;
