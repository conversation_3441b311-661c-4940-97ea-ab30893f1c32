/**
 * 角色模板和品质定义
 */

// 角色品质枚举
export const QUALITY = {
    NORMAL: 0,   // 普通
    RARE: 1,     // 稀有
    EPIC: 2,     // 史诗
    LEGENDARY: 3, // 传说
    DIVINE: 4    // 神话
};

// 角色品质名称
export const QUALITY_NAMES = {
    [QUALITY.NORMAL]: '普通',
    [QUALITY.RARE]: '稀有',
    [QUALITY.EPIC]: '史诗',
    [QUALITY.LEGENDARY]: '传说',
    [QUALITY.DIVINE]: '神话'
};

// 角色品质颜色
export const QUALITY_COLORS = {
    [QUALITY.NORMAL]: '#FFFFFF',   // 白色
    [QUALITY.RARE]: '#00FF00',     // 绿色
    [QUALITY.EPIC]: '#0000FF',     // 蓝色
    [QUALITY.LEGENDARY]: '#FF00FF', // 紫色
    [QUALITY.DIVINE]: '#FFD700'     // 金色
};

// 角色基础属性系数（基于品质）
const BASE_ATTRIBUTES = {
    [QUALITY.NORMAL]: {
        hp: 100,
        attack: 10,
        defense: 5,
        speed: 10,
        critRate: 5,
        critDamage: 150,
        daoRule: 0,
        penetration: 0
    },
    [QUALITY.RARE]: {
        hp: 120,
        attack: 12,
        defense: 6,
        speed: 10,
        critRate: 7,
        critDamage: 160,
        daoRule: 1,
        penetration: 1
    },
    [QUALITY.EPIC]: {
        hp: 150,
        attack: 15,
        defense: 7,
        speed: 11,
        critRate: 10,
        critDamage: 170,
        daoRule: 2,
        penetration: 2
    },
    [QUALITY.LEGENDARY]: {
        hp: 180,
        attack: 18,
        defense: 9,
        speed: 12,
        critRate: 12,
        critDamage: 180,
        daoRule: 3,
        penetration: 3
    },
    [QUALITY.DIVINE]: {
        hp: 220,
        attack: 22,
        defense: 11,
        speed: 13,
        critRate: 15,
        critDamage: 200,
        daoRule: 5,
        penetration: 5
    }
};

// 等级成长系数（线性成长）
const LEVEL_GROWTH = {
    hp: 10,        // 每级+10生命
    attack: 1,      // 每级+1攻击
    defense: 0.5,   // 每级+0.5防御
    speed: 0.1,     // 每级+0.1速度
    critRate: 0.2,  // 每级+0.2%暴击率
    critDamage: 1,  // 每级+1%暴击伤害
    daoRule: 0.1,   // 每级+0.1大道法则
    penetration: 0.1 // 每级+0.1破防
};

// 星级成长系数（每星级属性百分比加成）
const STAR_GROWTH_PERCENT = {
    hp: 10,        // 每星+10%生命
    attack: 10,     // 每星+10%攻击
    defense: 10,    // 每星+10%防御
    speed: 5,       // 每星+5%速度
    critRate: 5,    // 每星+5%暴击率
    critDamage: 10, // 每星+10%暴击伤害
    daoRule: 10,    // 每星+10%大道法则
    penetration: 10 // 每星+10%破防
};

// 升星所需的角色碎片
const STAR_UPGRADE_COST = {
    0: 10,   // 0星升1星需要10碎片
    1: 20,   // 1星升2星需要20碎片
    2: 40,   // 2星升3星需要40碎片
    3: 80,   // 3星升4星需要80碎片
    4: 160,  // 4星升5星需要160碎片
    5: 320   // 5星升6星需要320碎片
};

/**
 * 获取角色升星所需碎片数量
 * @param {number} currentStar 当前星级
 * @returns {number} 升级所需碎片数量
 */
export function getStarUpgradeCost(currentStar) {
    return STAR_UPGRADE_COST[currentStar] || Infinity; // 如果超过定义的范围，返回无限大
}

/**
 * 根据角色品质、等级和星级生成属性
 * @param {number} quality 角色品质
 * @param {number} level 角色等级
 * @param {number} star 角色星级
 * @returns {Object} 生成的角色属性
 */
export function generateCharacterAttributes(quality, level, star) {
    const baseAttr = BASE_ATTRIBUTES[quality];
    const attributes = {};

    // 计算所有属性
    for (const key in baseAttr) {
        // 基础值 + 等级成长
        attributes[key] = baseAttr[key] + (level - 1) * LEVEL_GROWTH[key];

        // 应用星级加成（百分比）
        if (star > 0) {
            attributes[key] *= (1 + star * STAR_GROWTH_PERCENT[key] / 100);
        }

        // 确保数值不会出现负数或者超过最大值
        if (key === 'critRate') {
            attributes[key] = Math.min(100, attributes[key]); // 暴击率最高100%
        } else if (key !== 'penetration') {
            attributes[key] = Math.max(0, attributes[key]); // 非穿透属性不能为负
        }

        // 为整数属性取整
        if (['hp', 'attack', 'defense'].includes(key)) {
            attributes[key] = Math.floor(attributes[key]);
        }
    }

    return attributes;
}

/**
 * 角色列表
 * 预定义的角色模板（已移除可抽取角色，只保留女剑仙作为主角）
 */
export const CHARACTER_TEMPLATES = [
    // 主角角色
    {
        id: 'char_main_1',
        name: '女剑仙',
        quality: QUALITY.DIVINE,
        description: '天赋异禀的剑修，注定要在修仙路上闯出一番天地。'
    }
];

/**
 * 获取角色模板通过ID
 * @param {string} templateId 模板ID
 * @returns {Object|null} 角色模板
 */
export function getCharacterTemplate(templateId) {
    return CHARACTER_TEMPLATES.find(template => template.id === templateId) || null;
}

/**
 * 根据品质获取角色列表
 * @param {number} quality 角色品质
 * @returns {Array} 指定品质的角色列表
 */
export function getCharactersByQuality(quality) {
    return CHARACTER_TEMPLATES.filter(template => template.quality === quality);
}

/**
 * 创建角色实例
 * @param {string} templateId 角色模板ID
 * @param {number} level 角色等级
 * @param {number} star 角色星级
 * @returns {Object} 角色参数
 */
export function createCharacterFromTemplate(templateId, level = 1, star = 0) {
    const template = getCharacterTemplate(templateId);
    if (!template) {
        throw new Error(`未找到模板ID：${templateId}`);
    }

    const attributes = generateCharacterAttributes(template.quality, level, star);

    return {
        id: template.id,
        name: template.name,
        quality: template.quality,
        level,
        star,
        fragments: 0,
        ...attributes
    };
}