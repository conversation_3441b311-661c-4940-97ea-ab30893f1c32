/**
 * 角色详情页面场景类
 * 展示角色详细信息
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import game from '../../game';
import AppContext from '../utils/AppContext';
import REALM_CONFIG from '../config/RealmConfig.js';

class CharacterDetailScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager) {
    super(ctx, screenWidth, screenHeight, sceneManager);

    // 场景资源
    this.resources = null;

    // 当前角色
    this.character = null;

    // 页面状态
    this.pageTitle = '详情';

    // 当前选中的功能标签（0: 基础, 1: 装备, 2: 功法, 3: 进阶）
    this.selectedTabIndex = 0;

    // 装备界面中选中的装备槽位
    this.selectedEquipSlot = 0;

    // 初始化UI
    this.initUI();
  }

  // 初始化UI
  initUI() {
    // 创建返回按钮
    const backButtonSize = 40;
    const margin = 10;

    this.backButton = new Button(
      this.ctx,
      this.screenWidth - backButtonSize - margin,
      this.screenHeight - backButtonSize - margin,
      backButtonSize,
      backButtonSize,
      '返回',
      null,
      null,
      () => {
        // 返回角色页面
        this.sceneManager.showScene('character');
      }
    );

    this.addUIElement(this.backButton);

    // 创建功能标签按钮 - 改为装备栏下方显示
    const tabButtonSize = 50;
    const tabButtonMargin = 10;
    const tabButtonY = 380; // 调整位置到装备栏下方

    // 基础信息按钮
    this.basicButton = new Button(
      this.ctx,
      this.screenWidth / 2 - tabButtonSize * 2 - tabButtonMargin,
      tabButtonY,
      tabButtonSize,
      tabButtonSize,
      '基础',
      null,
      null,
      () => {
        this.selectedTabIndex = 0;
        this.updateTabUI();
      }
    );

    this.addUIElement(this.basicButton);

    // 装备按钮
    this.equipButton = new Button(
      this.ctx,
      this.screenWidth / 2 - tabButtonSize / 2,
      tabButtonY,
      tabButtonSize,
      tabButtonSize,
      '装备',
      null,
      null,
      () => {
        this.selectedTabIndex = 1;
        this.updateTabUI();
      }
    );

    this.addUIElement(this.equipButton);

    // 功法按钮
    this.skillButton = new Button(
      this.ctx,
      this.screenWidth / 2 + tabButtonSize / 2 + tabButtonMargin,
      tabButtonY,
      tabButtonSize,
      tabButtonSize,
      '功法',
      null,
      null,
      () => {
        this.selectedTabIndex = 2;
        this.updateTabUI();
      }
    );

    this.addUIElement(this.skillButton);

    // 进阶按钮
    this.advanceButton = new Button(
      this.ctx,
      this.screenWidth / 2 + tabButtonSize * 3/2 + tabButtonMargin * 2,
      tabButtonY,
      tabButtonSize,
      tabButtonSize,
      '进阶',
      null,
      null,
      () => {
        this.selectedTabIndex = 3;
        this.updateTabUI();
      }
    );

    this.addUIElement(this.advanceButton);

    // 不再创建修炼按钮，修炼功能移至静室

    // 创建一键强化按钮
    this.upgradeButton = new Button(
      this.ctx,
      this.screenWidth / 2 - 60,
      tabButtonY + 80,
      120,
      40,
      '一键强化',
      null,
      null,
      () => {
        this.upgradeEquipment();
      }
    );

    // 创建装备功法按钮
    this.equipSkillButton = new Button(
      this.ctx,
      this.screenWidth / 2 - 130,
      tabButtonY + 80,
      120,
      40,
      '装备功法',
      null,
      null,
      () => {
        this.sceneManager.showScene('skill', { character: this.character });
      }
    );

    // 创建升级功法按钮
    this.upgradeSkillButton = new Button(
      this.ctx,
      this.screenWidth / 2 + 10,
      tabButtonY + 80,
      120,
      40,
      '升级功法',
      null,
      null,
      () => {
        if (this.character && this.character.equippedSkill) {
          // 传递功法对象本身，而不是依赖skillId
          console.log('跳转到功法升级场景，携带功法对象', this.character.equippedSkill);
          this.sceneManager.showScene('skillUpgrade', {
            skill: this.character.equippedSkill,
            skillId: this.character.equippedSkill.id // 同时传递ID作为备用
          });
        } else {
          console.log('未装备功法，无法升级');
        }
      }
    );

    // 创建角色升级按钮
    const gameStateManager = AppContext.game.gameStateManager;
    const player = gameStateManager.getPlayer();
    const resources = player.resources || {};
    const expPotions = resources.expPotion || 0;

    this.upgradeCharButton = new Button(
      this.ctx,
      this.screenWidth / 2 - 80,
      tabButtonY + 50,
      160,
      40,
      `升级 (消耗1修为丹)`,
      null,
      expPotions < 1 ? 'rgba(100, 100, 100, 0.7)' : null,
      () => this.upgradeCharacter()
    );

    // 创建升星按钮
    if (this.character) {
      const fragments = this.character.fragments || 0;
      const currentStarCost = this.getStarUpgradeCost(this.character.star || 1);

      this.starUpButton = new Button(
        this.ctx,
        this.screenWidth / 2 - 80,
        tabButtonY + 110,
        160,
        40,
        `升星 (需要${currentStarCost}碎片)`,
        null,
        fragments < currentStarCost || (this.character.star || 1) >= 6 ? 'rgba(100, 100, 100, 0.7)' : null,
        () => this.upgradeCharacterStar()
      );
    }
  }

  // 场景显示时的回调
  onShow(params) {
    try {
      console.log("CharacterDetailScene onShow", params);
      this.visible = true;

      // 确保能获取到游戏资源
      this.resources = game.resourceLoader.resources;

      // 清空UI元素
      this.clearUIElements();

      // 可以接受character对象或characterId
      if (params && params.character) {
        // 直接使用传入的character对象
        this.character = params.character;
        this.characterId = params.character.id;
        console.log(`成功加载角色详情页，角色名称: ${this.character.name}`);
      } else if (params && params.characterId) {
        // 从characterId获取角色数据
        this.characterId = params.characterId;
        this.character = game.gameStateManager.getCharacterById(this.characterId);

        if (!this.character) {
          console.error(`找不到ID为${this.characterId}的角色`);
          return;
        }

        console.log(`成功加载角色详情页，角色ID: ${this.characterId}`);
      } else {
        console.error('角色详情页缺少必要参数：characterId或character');
        return;
      }

      // 初始化UI
      this.initUI();

      // 默认选择基础信息标签
      this.selectedTabIndex = 0;
      this.updateTabUI();
    } catch (error) {
      console.error("CharacterDetailScene.onShow 出错:", error);
    }
  }

  // 更新标签UI
  updateTabUI() {
    // 移除之前的特定按钮
    this.removeUIElement(this.cultivateButton);
    this.removeUIElement(this.upgradeButton);
    this.removeUIElement(this.equipSkillButton);
    this.removeUIElement(this.upgradeSkillButton);
    this.removeUIElement(this.upgradeCharButton);
    this.removeUIElement(this.starUpButton);

    // 根据当前选择的标签添加对应的按钮
    if (this.selectedTabIndex === 0) {
      // 基础信息标签，不再添加修炼按钮
      // 修炼功能移至静室
    } else if (this.selectedTabIndex === 1) {
      // 装备标签，添加强化按钮
      this.addUIElement(this.upgradeButton);
    } else if (this.selectedTabIndex === 2) {
      // 功法标签，添加装备和升级功法按钮
      this.addUIElement(this.equipSkillButton);
      this.addUIElement(this.upgradeSkillButton);
    } else if (this.selectedTabIndex === 3) {
      // 进阶标签，添加升级和升星按钮
      this.addUIElement(this.upgradeCharButton);
      this.addUIElement(this.starUpButton);
    }
  }

  // 修炼角色功能已移至静室
  // cultivateCharacter方法已移除

  // 强化装备
  upgradeEquipment() {
    if (this.character) {
      // 获取当前选中的装备
      const equipment = this.character.equipments[this.selectedEquipSlot];

      if (equipment) {
        // 强化装备
        const success = equipment.upgrade();

        // 更新游戏状态
        game.gameStateManager.updateCharacter(this.character.id, this.character);

        // 显示强化结果
        if (success) {
          console.log(`装备强化成功！`);
        } else {
          console.log(`装备强化失败！`);
        }
      }
    }
  }

  // 获取升星消耗的碎片数量
  getStarUpgradeCost(currentStar) {
    const costs = [0, 20, 40, 60, 100, 150]; // 索引对应星级，从1星升到2星需要20碎片
    return costs[currentStar] || 999;
  }

  // 升级角色
  upgradeCharacter() {
    const gameStateManager = AppContext.game.gameStateManager;
    const player = gameStateManager.getPlayer();
    if (!player.resources || player.resources.expPotion < 1) {
      console.log('修为丹不足！');
      return;
    }

    // 扣除修为丹
    player.resources.expPotion -= 1;

    // 增加角色等级和经验
    this.character.level += 1;

    // 更新角色的能力值
    this.character.hp += 10;
    this.character.attack += 2;
    this.character.defense += 1;

    // 保存角色和玩家数据
    gameStateManager.updateCharacter(this.character.id, this.character);
    gameStateManager.setPlayer(player);

    // 刷新UI
    this.initUI();
  }

  // 升级角色星级
  upgradeCharacterStar() {
    const gameStateManager = AppContext.game.gameStateManager;
    const fragments = this.character.fragments || 0;
    const currentStarCost = this.getStarUpgradeCost(this.character.star || 1);

    if (fragments < currentStarCost) {
      console.log('角色碎片不足！');
      return;
    }

    if ((this.character.star || 1) >= 6) {
      console.log('已达到最高星级！');
      return;
    }

    // 确保角色有星级属性
    if (!this.character.star) {
      this.character.star = 1;
    }

    // 扣除碎片
    this.character.fragments -= currentStarCost;

    // 增加星级
    this.character.star += 1;

    // 更新角色的能力值 (星级加成)
    this.character.hp *= 1.1;
    this.character.attack *= 1.1;
    this.character.defense *= 1.1;

    // 保存角色数据
    gameStateManager.updateCharacter(this.character.id, this.character);

    // 刷新UI
    this.initUI();
  }

  // 处理触摸开始事件
  handleTouchStart(x, y) {
    console.log(`CharacterDetailScene handleTouchStart: ${x},${y}`);

    // 检查是否点击了装备槽位
    if (this.selectedTabIndex === 1) {
      const slot = this.getEquipSlotAtPosition(x, y);
      if (slot !== -1) {
        this.selectedEquipSlot = slot;
        return true;
      }
    }

    return false;
  }

  // 处理触摸移动事件
  handleTouchMove(x, y) {
    return false;
  }

  // 处理触摸结束事件
  handleTouchEnd(x, y) {
    if (this.character) {
      // 检查是否点击了装备槽位（无论当前选中哪个标签）
      const slotIndex = this.getEquipSlotAtPosition(x, y);
      if (slotIndex !== -1) {
        this.selectedEquipSlot = slotIndex;

        // 装备类型名称
        const equipTypeNames = ['武器', '护甲', '饰品', '灵宝'];
        const slotType = equipTypeNames[slotIndex];

        console.log(`点击了装备槽位: ${slotIndex}, 类型: ${slotType}`);

        // 打开装备选择页面
        this.sceneManager.showScene('equipmentSelect', {
          character: this.character,
          slotType: slotType
        });

        return true;
      }
    }

    return false;
  }

  // 获取指定位置的装备槽位
  getEquipSlotAtPosition(x, y) {
    if (!this.character) return -1;

    // 装备槽位区域
    const equipSlotY = this.screenHeight * 0.6;
    const equipSlotSize = 50;
    const spacing = 10;
    // 修改为4个装备槽
    const totalWidth = equipSlotSize * 4 + spacing * 3;
    const startX = (this.screenWidth - totalWidth) / 2;

    // 检查是否在槽位行上
    if (y >= equipSlotY && y <= equipSlotY + equipSlotSize) {
      // 计算点击的是哪个槽位
      for (let i = 0; i < 4; i++) {
        const slotX = startX + i * (equipSlotSize + spacing);
        if (x >= slotX && x <= slotX + equipSlotSize) {
          return i;
        }
      }
    }

    return -1;
  }

  // 子类实现的绘制逻辑
  drawScene() {
    if (!this.character) {
      return;
    }

    // 绘制半透明背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);

    // 绘制顶部导航栏
    this.drawHeader();

    // 绘制角色名称
    this.drawCharacterName();

    // 绘制角色图像
    this.drawCharacterImage();

    // 绘制装备栏
    this.drawEquipmentSlots();

    // 绘制功能区域
    this.drawTabContent();

    // 绘制功能标签按钮
    this.drawTabButtons();
  }

  // 绘制顶部导航栏
  drawHeader() {
    const headerHeight = 80;

    // 绘制顶部导航栏背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, 0, this.screenWidth, headerHeight);

    // 绘制页面标题
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(this.pageTitle, 20, headerHeight / 2 + 8);

    // 绘制资源信息
    const player = game.gameStateManager.getPlayer();
    const resourceY = headerHeight / 2;
    const iconSize = 20;

    // 绘制仙玉
    if (this.resources && this.resources.iconXianyu) {
      this.ctx.drawImage(
        this.resources.iconXianyu,
        this.screenWidth / 2,
        resourceY - iconSize / 2,
        iconSize,
        iconSize
      );
    } else {
      // 如果没有图标资源，绘制简单的圆形
      this.ctx.fillStyle = '#ffd700';
      this.ctx.beginPath();
      this.ctx.arc(this.screenWidth / 2 + iconSize / 2, resourceY, iconSize / 2, 0, Math.PI * 2);
      this.ctx.fill();
    }

    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`仙玉: ${player.resources.xianyu}`, this.screenWidth / 2 + iconSize + 5, resourceY + 5);

    // 绘制灵石
    const lingshiX = this.screenWidth / 2 + 120;

    if (this.resources && this.resources.iconLingshi) {
      this.ctx.drawImage(
        this.resources.iconLingshi,
        lingshiX,
        resourceY - iconSize / 2,
        iconSize,
        iconSize
      );
    } else {
      // 如果没有图标资源，绘制简单的方形
      this.ctx.fillStyle = '#c0c0c0';
      this.ctx.fillRect(lingshiX, resourceY - iconSize / 2, iconSize, iconSize);
    }

    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`灵石: ${player.resources.lingshi}`, lingshiX + iconSize + 5, resourceY + 5);
  }

  // 绘制角色名称
  drawCharacterName() {
    const headerHeight = 80;

    // 获取角色品质信息
    const quality = this.character.quality || 0;

    // 品质名称和颜色
    const qualityNames = {
      0: '普通',
      1: '稀有',
      2: '史诗',
      3: '传说',
      4: '神话'
    };

    const qualityColors = {
      0: '#FFFFFF',   // 白色
      1: '#00FF00',   // 绿色
      2: '#0000FF',   // 蓝色
      3: '#FF00FF',   // 紫色
      4: '#FFD700'    // 金色
    };

    const qualityName = qualityNames[quality] || '普通';
    const qualityColor = qualityColors[quality] || '#FFFFFF';

    // 绘制角色品质背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    const qualityBgWidth = 80;
    const qualityBgHeight = 24;
    const qualityBgX = this.screenWidth / 2 - qualityBgWidth / 2;
    const qualityBgY = headerHeight + 5;
    this.ctx.fillRect(qualityBgX, qualityBgY, qualityBgWidth, qualityBgHeight);

    // 绘制角色品质
    this.ctx.font = 'bold 16px Arial';
    this.ctx.fillStyle = qualityColor;
    this.ctx.textAlign = 'center';
    this.ctx.fillText(qualityName, this.screenWidth / 2, headerHeight + 22);

    // 绘制角色名称
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(this.character.name, this.screenWidth / 2, headerHeight + 50);

    // 绘制角色星级
    const star = this.character.star || 0;
    const starText = '★'.repeat(star) + '☆'.repeat(Math.max(0, 6 - star));
    this.ctx.font = '18px Arial';
    this.ctx.fillStyle = '#FFD700';
    this.ctx.fillText(starText, this.screenWidth / 2, headerHeight + 75);

    // 绘制角色战力
    this.ctx.font = '18px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.fillText(`战力: ${this.character.power}`, this.screenWidth / 2, headerHeight + 100);
  }

  // 绘制角色图像
  drawCharacterImage() {
    // 绘制角色图像，占据屏幕下方三分之二的区域
    const headerHeight = 80;
    const characterNameHeight = 100; // 增加高度，因为我们添加了品质和星级
    const characterImgY = headerHeight + characterNameHeight;
    const characterImgHeight = Math.floor(this.screenHeight * 0.35); // 稍微减少高度

    // 获取角色品质信息
    const quality = this.character.quality || 0;
    const qualityColors = {
      0: 'rgba(255, 255, 255, 0.2)',   // 白色
      1: 'rgba(0, 255, 0, 0.2)',      // 绿色
      2: 'rgba(0, 0, 255, 0.2)',      // 蓝色
      3: 'rgba(255, 0, 255, 0.2)',    // 紫色
      4: 'rgba(255, 215, 0, 0.2)'     // 金色
    };
    const qualityColor = qualityColors[quality] || 'rgba(255, 255, 255, 0.2)';

    // 绘制品质光晕背景
    const glowSize = characterImgHeight * 0.7;
    const gradient = this.ctx.createRadialGradient(
      this.screenWidth / 2, characterImgY + characterImgHeight / 2, 10,
      this.screenWidth / 2, characterImgY + characterImgHeight / 2, glowSize
    );
    gradient.addColorStop(0, qualityColor.replace('0.2', '0.5'));
    gradient.addColorStop(1, 'rgba(0, 0, 0, 0)');

    this.ctx.fillStyle = gradient;
    this.ctx.beginPath();
    this.ctx.arc(this.screenWidth / 2, characterImgY + characterImgHeight / 2, glowSize, 0, Math.PI * 2);
    this.ctx.fill();

    // 如果有角色图片资源，绘制角色图片
    const characterImgKey = `character${this.character.id}`;
    if (this.resources && this.resources[characterImgKey]) {
      this.ctx.drawImage(
        this.resources[characterImgKey],
        (this.screenWidth - characterImgHeight * 0.5) / 2, // 假设图片宽高比为1:2
        characterImgY,
        characterImgHeight * 0.5,
        characterImgHeight
      );
    } else {
      // 如果没有角色图片资源，绘制占位图
      // 绘制带品质颜色的圆形背景
      this.ctx.fillStyle = qualityColor.replace('0.2', '0.5');
      this.ctx.beginPath();
      this.ctx.arc(
        this.screenWidth / 2,
        characterImgY + characterImgHeight / 2,
        characterImgHeight * 0.3,
        0,
        Math.PI * 2
      );
      this.ctx.fill();

      // 绘制角色名称
      this.ctx.font = 'bold 40px Arial';
      this.ctx.fillStyle = '#ffffff';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
      this.ctx.fillText(
        this.character.name,
        this.screenWidth / 2,
        characterImgY + characterImgHeight / 2
      );
    }

    // 绘制角色境界
    this.ctx.font = 'bold 18px Arial';
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'top';
    this.ctx.fillText(
      `境界: ${this.character.cultivation}`,
      this.screenWidth / 2,
      characterImgY + characterImgHeight + 10
    );
  }

  // 绘制装备槽位
  drawEquipmentSlots() {
    // 装备槽位区域
    const equipSlotY = this.screenHeight * 0.6;
    const equipSlotSize = 50;
    const spacing = 10;
    // 修改为4个装备槽
    const totalWidth = equipSlotSize * 4 + spacing * 3;
    const startX = (this.screenWidth - totalWidth) / 2;

    // 装备类型名称和对应的属性名
    const equipTypeNames = ['武器', '护甲', '饰品', '灵宝'];
    const equipTypeKeys = ['weapon', 'armor', 'accessory', 'artifact'];

    // 绘制4个装备槽位
    for (let i = 0; i < 4; i++) {
      const x = startX + i * (equipSlotSize + spacing);
      const y = equipSlotY;

      // 获取当前槽位的装备 - 修正装备获取方式
      const equipment = this.character.equipment ? this.character.equipment[equipTypeKeys[i]] : null;

      // 绘制装备槽位
      if (this.resources && this.resources.equipFrame) {
        this.ctx.drawImage(
          this.resources.equipFrame,
          x,
          y,
          equipSlotSize,
          equipSlotSize
        );
      } else {
        // 如果没有装备框资源，绘制简单的方框
        this.ctx.fillStyle = 'rgba(50, 50, 50, 0.7)';
        this.ctx.fillRect(x, y, equipSlotSize, equipSlotSize);

        this.ctx.strokeStyle = i === this.selectedEquipSlot ? '#ffd700' : '#ffffff';
        this.ctx.lineWidth = 2;
        this.ctx.strokeRect(x, y, equipSlotSize, equipSlotSize);
      }

      // 如果有装备，绘制装备图标
      if (equipment) {
        // 根据装备品质设置颜色
        const qualityColors = {
          0: '#CCCCCC', // 普通 (灰色)
          1: '#55AA55', // 良好 (绿色)
          2: '#5555AA', // 稀有 (蓝色)
          3: '#AA55AA', // 史诗 (紫色)
          4: '#AAAA55'  // 传说 (金色)
        };
        const qualityColor = qualityColors[equipment.quality] || '#FFFFFF';

        // 绘制装备底色
        this.ctx.fillStyle = qualityColor;
        this.ctx.globalAlpha = 0.5;
        this.ctx.beginPath();
        this.ctx.arc(x + equipSlotSize / 2, y + equipSlotSize / 2, equipSlotSize / 3, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.globalAlpha = 1.0;

        // 绘制装备名称首字母
        this.ctx.font = 'bold 18px Arial';
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(equipment.name.charAt(0), x + equipSlotSize / 2, y + equipSlotSize / 2 + 6);

        // 绘制装备等级
        this.ctx.font = 'bold 12px Arial';
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(`+${equipment.level}`, x + equipSlotSize / 2, y + equipSlotSize - 5);
      }

      // 绘制装备类型名称
      this.ctx.font = '12px Arial';
      this.ctx.fillStyle = '#FFFFFF';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(equipTypeNames[i], x + equipSlotSize / 2, y + equipSlotSize + 15);
    }
  }

  // 绘制功能标签按钮
  drawTabButtons() {
    const tabButtonSize = 50;
    const tabButtonMargin = 10;
    const tabButtonY = this.screenHeight - 150;

    // 绘制功能标签背景
    this.ctx.fillStyle = 'rgba(50, 50, 50, 0.7)';
    this.ctx.fillRect(
      this.screenWidth - tabButtonSize * 4 - tabButtonMargin * 3 - 10,
      tabButtonY - 10,
      tabButtonSize * 4 + tabButtonMargin * 3 + 20,
      tabButtonSize + 20
    );

    // 绘制选中标签的指示器
    this.ctx.fillStyle = '#ffd700';
    this.ctx.fillRect(
      this.screenWidth - tabButtonSize * 4 - tabButtonMargin * 3 + this.selectedTabIndex * (tabButtonSize + tabButtonMargin) - 3,
      tabButtonY - 3,
      tabButtonSize + 6,
      6
    );
  }

  // 绘制功能区域内容
  drawTabContent() {
    const contentAreaY = this.screenHeight * 0.7;
    const contentAreaHeight = this.screenHeight * 0.2;

    // 绘制功能区域背景
    this.ctx.fillStyle = 'rgba(30, 30, 30, 0.7)';
    this.ctx.fillRect(10, contentAreaY, this.screenWidth - 20, contentAreaHeight);

    // 根据当前选中的标签绘制不同的内容
    switch (this.selectedTabIndex) {
      case 0:
        // 基础信息
        this.drawBasicInfo(10, contentAreaY, this.screenWidth - 20, contentAreaHeight);
        break;
      case 1:
        // 装备信息
        this.drawEquipmentInfo(10, contentAreaY, this.screenWidth - 20, contentAreaHeight);
        break;
      case 2:
        // 功法信息
        this.drawSkillInfo(10, contentAreaY, this.screenWidth - 20, contentAreaHeight);
        break;
      case 3:
        // 进阶信息
        this.drawAdvanceInfo(10, contentAreaY, this.screenWidth - 20, contentAreaHeight);
        break;
    }
  }

  // 绘制基础信息
  drawBasicInfo(x, y, width, height) {
    // 获取角色属性
    const attributes = this.character.getAttributes();

    // 绘制属性背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    this.ctx.fillRect(x + 10, y + 10, width - 20, height - 20);

    // 绘制基础属性
    this.ctx.font = 'bold 18px Arial';
    this.ctx.fillStyle = '#FFCC00';
    this.ctx.textAlign = 'left';
    this.ctx.fillText('基础属性', x + 20, y + 30);

    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#ffffff';

    // 只显示基础属性
    const attributeLabels = [
      `生命值: ${Math.floor(attributes.hp)}`,
      `法力: ${Math.floor(attributes.mp)}`,
      `攻击力: ${Math.floor(attributes.attack)}`,
      `防御力: ${Math.floor(attributes.defense)}`,
      `速度: ${Math.floor(attributes.speed)}`
    ];

    attributeLabels.forEach((label, index) => {
      this.ctx.fillText(label, x + 20, y + 55 + index * 25);
    });

    // 添加查看全部属性的按钮
    const buttonX = x + width - 50;
    const buttonY = y + 30;
    const buttonRadius = 15;

    // 绘制圆形按钮
    this.ctx.fillStyle = '#4CAF50';
    this.ctx.beginPath();
    this.ctx.arc(buttonX, buttonY, buttonRadius, 0, Math.PI * 2);
    this.ctx.fill();

    // 绘制按钮文字
    this.ctx.font = 'bold 16px Arial';
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText('?', buttonX, buttonY);

    // 添加按钮点击区域
    this.uiElements.push({
      type: 'button',
      x: buttonX - buttonRadius,
      y: buttonY - buttonRadius,
      width: buttonRadius * 2,
      height: buttonRadius * 2,
      onClick: () => {
        this.showAllAttributes();
      }
    });

    // 绘制灵力信息
    this.ctx.font = 'bold 16px Arial';
    this.ctx.fillStyle = '#FFCC00';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('修炼信息', x + width / 2, y + height - 70);

    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#ffffff';

    // 从RealmConfig获取突破所需灵力
    let requiredLingli = 0;

    // 根据角色等级获取下一级所需灵力
    const currentLevel = this.character.level || 1;
    const nextLevelConfig = REALM_CONFIG.find(config => config.level === currentLevel + 1);

    if (nextLevelConfig) {
      requiredLingli = nextLevelConfig.requiredLingLi;
    } else {
      // 如果找不到下一级配置，使用最后一级配置的2倍作为默认值
      const lastConfig = REALM_CONFIG[REALM_CONFIG.length - 1];
      requiredLingli = lastConfig ? lastConfig.requiredLingLi * 2 : 1000000;
    }

    // 获取玩家当前灵力
    const player = game.gameStateManager.getPlayer();
    const currentLingli = player && player.resources ? (player.resources.lingli || 0) : 0;

    // 绘制灵力进度条
    const lingliBarWidth = width - 60;
    const lingliBarHeight = 16;
    const lingliBarX = x + 30;
    const lingliBarY = y + height - 40;

    // 灵力条背景
    this.ctx.fillStyle = 'rgba(100, 100, 100, 0.5)';
    this.ctx.fillRect(lingliBarX, lingliBarY, lingliBarWidth, lingliBarHeight);

    // 灵力条进度
    const lingliProgress = Math.min(1, currentLingli / requiredLingli);
    this.ctx.fillStyle = '#4CAF50';
    this.ctx.fillRect(lingliBarX, lingliBarY, lingliBarWidth * lingliProgress, lingliBarHeight);

    // 绘制灵力数值
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(`灵力: ${currentLingli}/${requiredLingli}`, x + width / 2, lingliBarY - 5);

    // 添加提示文字
    this.ctx.fillStyle = '#FFCC00';
    this.ctx.fillText('前往静室修炼提升灵力', x + width / 2, lingliBarY + 30);
  }

  // 显示所有属性
  showAllAttributes() {
    // 获取角色属性
    const attributes = this.character.getAttributes();

    // 创建属性列表
    const basicAttributes = [
      `生命值: ${Math.floor(attributes.hp)}`,
      `法力: ${Math.floor(attributes.mp)}`,
      `攻击力: ${Math.floor(attributes.attack)}`,
      `防御力: ${Math.floor(attributes.defense)}`,
      `速度: ${Math.floor(attributes.speed)}`
    ];

    const advancedAttributes = [
      `暴击率: ${(attributes.critRate * 100).toFixed(1)}%`,
      `暴击伤害: ${(attributes.critDamage * 100).toFixed(1)}%`,
      `大道法则: ${attributes.daoRule.toFixed(1)}`,
      `穿透: ${attributes.penetration.toFixed(1)}`,
      `生命加成: ${(attributes.hpBonus * 100).toFixed(1)}%`,
      `攻击加成: ${(attributes.attackBonus * 100).toFixed(1)}%`,
      `防御加成: ${(attributes.defenseBonus * 100).toFixed(1)}%`,
      `伤害加成: ${(attributes.damageBonus * 100).toFixed(1)}%`,
      `闪避率: ${(attributes.dodgeRate * 100).toFixed(1)}%`,
      `治疗效果: ${(attributes.healEffect * 100).toFixed(1)}%`,
      `伤害减免: ${(attributes.damageReduction * 100).toFixed(1)}%`
    ];

    // 使用wx.showModal显示属性
    wx.showModal({
      title: `${this.character.name} 的全部属性`,
      content: `基础属性:\n${basicAttributes.join('\n')}\n\n进阶属性:\n${advancedAttributes.join('\n')}`,
      showCancel: false,
      confirmText: '关闭'
    });
  }

  // 绘制装备信息
  drawEquipmentInfo(x, y, width, height) {
    // 绘制装备小按钮
    const equipButtonWidth = width / 4; // 修改为4个按钮
    const equipButtonHeight = 30;
    const equipButtonY = y + 10;

    const equipTypeNames = ['武器', '护甲', '饰品', '灵宝']; // 修改为4种装备类型
    const equipTypeKeys = ['weapon', 'armor', 'accessory', 'artifact']; // 对应的属性键

    for (let i = 0; i < 4; i++) { // 修改为4个按钮
      const buttonX = x + i * equipButtonWidth;

      // 绘制按钮背景
      this.ctx.fillStyle = i === this.selectedEquipSlot ? 'rgba(70, 130, 180, 0.7)' : 'rgba(50, 50, 50, 0.7)';
      this.ctx.fillRect(buttonX, equipButtonY, equipButtonWidth, equipButtonHeight);

      // 绘制按钮边框
      this.ctx.strokeStyle = i === this.selectedEquipSlot ? '#ffd700' : '#ffffff';
      this.ctx.lineWidth = 1;
      this.ctx.strokeRect(buttonX, equipButtonY, equipButtonWidth, equipButtonHeight);

      // 绘制装备类型名称
      this.ctx.font = '14px Arial';
      this.ctx.fillStyle = '#ffffff';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(equipTypeNames[i], buttonX + equipButtonWidth / 2, equipButtonY + equipButtonHeight / 2 + 5);
    }

    // 获取当前选中的装备 - 修正装备获取方式
    const equipmentKey = equipTypeKeys[this.selectedEquipSlot];
    const equipment = this.character.equipment ? this.character.equipment[equipmentKey] : null;

    // 绘制装备详情
    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';

    if (equipment) {
      // 绘制装备名称
      this.ctx.fillText(`${equipment.name} +${equipment.level}`, x + 20, y + 70);

      // 绘制装备属性
      let attributesText = '';
      if (equipment.attributes) {
        if (equipment.attributes.hp) attributesText += `生命 +${equipment.attributes.hp}\n`;
        if (equipment.attributes.attack) attributesText += `攻击 +${equipment.attributes.attack}\n`;
        if (equipment.attributes.defense) attributesText += `防御 +${equipment.attributes.defense}\n`;
        if (equipment.attributes.speed) attributesText += `速度 +${equipment.attributes.speed}\n`;

        // 特殊属性
        if (equipment.attributes.critRate) {
          attributesText += `暴击率 +${(equipment.attributes.critRate * 100).toFixed(1)}%\n`;
        }
        if (equipment.attributes.critDamage) {
          attributesText += `暴击伤害 +${(equipment.attributes.critDamage * 100).toFixed(1)}%\n`;
        }
        if (equipment.attributes.penetration) {
          attributesText += `破防 +${equipment.attributes.penetration}\n`;
        }
      }

      // 如果设备没有getAttributesDescription方法，就使用我们自己生成的文本
      const attributeLines = attributesText ? attributesText.split('\n') : (
        equipment.getAttributesDescription ?
        equipment.getAttributesDescription().split('\n') :
        ['无属性']
      );

      // 绘制装备品质
      const qualityColors = {
        0: '#CCCCCC', // 普通 (灰色)
        1: '#55AA55', // 良好 (绿色)
        2: '#5555AA', // 稀有 (蓝色)
        3: '#AA55AA', // 史诗 (紫色)
        4: '#AAAA55'  // 传说 (金色)
      };
      const qualityNames = {
        0: '普通',
        1: '良好',
        2: '稀有',
        3: '史诗',
        4: '传说'
      };

      const qualityColor = qualityColors[equipment.quality] || '#FFFFFF';
      const qualityName = qualityNames[equipment.quality] || '未知';

      this.ctx.fillStyle = qualityColor;
      this.ctx.fillText(`品质: ${qualityName}`, x + 20, y + 100);

      // 恢复白色绘制属性
      this.ctx.fillStyle = '#FFFFFF';
      attributeLines.forEach((line, index) => {
        if (line && line.trim()) { // 只绘制非空行
          this.ctx.fillText(line, x + 20, y + 125 + index * 25);
        }
      });
    } else {
      // 如果没有装备，显示空槽位信息
      this.ctx.fillText(`未装备${equipTypeNames[this.selectedEquipSlot]}`, x + 20, y + 70);
      this.ctx.fillText('点击上方装备槽位选择装备', x + 20, y + 100);
    }
  }

  // 绘制功法信息
  drawSkillInfo(x, y, width, height) {
    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';

    // 绘制功法栏
    const skillSlotSize = 60;
    const skillSlotY = y + 30;
    const skillSlotX = x + 20;

    // 绘制功法槽背景
    this.ctx.fillStyle = 'rgba(50, 50, 50, 0.7)';
    this.ctx.fillRect(skillSlotX, skillSlotY, skillSlotSize, skillSlotSize);

    // 绘制功法槽边框
    this.ctx.strokeStyle = '#ffffff';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(skillSlotX, skillSlotY, skillSlotSize, skillSlotSize);

    // 绘制功法槽标题
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('功法', skillSlotX + skillSlotSize / 2, skillSlotY - 10);

    // 检查是否有装备的功法
    const equippedSkill = this.character.getEquippedSkill();

    if (equippedSkill) {
      try {
        // 绘制功法图标或名称
        const qualityColor = equippedSkill.getQualityColor ? equippedSkill.getQualityColor() : '#ffffff';
        this.ctx.fillStyle = qualityColor;
        this.ctx.font = 'bold 16px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(
          equippedSkill.name.charAt(0) + equippedSkill.name.charAt(1),
          skillSlotX + skillSlotSize / 2,
          skillSlotY + skillSlotSize / 2
        );

        // 绘制功法详情
        this.ctx.textAlign = 'left';
        this.ctx.font = '14px Arial';
        this.ctx.fillStyle = '#ffffff';

        // 功法名称和品质
        const qualityName = equippedSkill.getQualityName ? equippedSkill.getQualityName() : '未知';
        this.ctx.fillText(
          `${equippedSkill.name} (${qualityName})`,
          skillSlotX + skillSlotSize + 20,
          skillSlotY + 20
        );

        // 功法等级和星级
        this.ctx.fillText(
          `等级: ${equippedSkill.level}  星级: ${equippedSkill.stars}★`,
          skillSlotX + skillSlotSize + 20,
          skillSlotY + 45
        );

        // 功法战力
        this.ctx.fillStyle = '#ffcc00';
        this.ctx.fillText(
          `战力加成: +${equippedSkill.power}`,
          skillSlotX + skillSlotSize + 20,
          skillSlotY + 70
        );
      } catch (error) {
        console.error('绘制装备功法时出错:', error);
        // 出错时显示简单信息
        this.ctx.font = '12px Arial';
        this.ctx.fillStyle = '#ffffff';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(
          equippedSkill.name || '功法',
          skillSlotX + skillSlotSize / 2,
          skillSlotY + skillSlotSize / 2
        );
      }
    } else {
      // 绘制未装备提示
      this.ctx.font = '12px Arial';
      this.ctx.fillStyle = '#aaaaaa';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(
        '未装备',
        skillSlotX + skillSlotSize / 2,
        skillSlotY + skillSlotSize / 2
      );
    }

    // 绘制功法列表
    const skills = this.character.getSkills();

    if (skills && skills.length > 0) {
      const listY = skillSlotY + skillSlotSize + 20;

      this.ctx.font = 'bold 16px Arial';
      this.ctx.fillStyle = '#ffffff';
      this.ctx.textAlign = 'left';
      this.ctx.fillText('可用功法:', x + 20, listY);

      // 绘制功法列表
      const skillItemHeight = 30;
      const maxDisplaySkills = 5;

      for (let i = 0; i < Math.min(skills.length, maxDisplaySkills); i++) {
        const skill = skills[i];
        const itemY = listY + 25 + i * skillItemHeight;

        // 背景色，如果是当前装备的功法则高亮
        this.ctx.fillStyle = equippedSkill && equippedSkill.id === skill.id
          ? 'rgba(60, 100, 150, 0.7)'
          : 'rgba(50, 50, 50, 0.7)';

        this.ctx.fillRect(x + 20, itemY, width - 40, skillItemHeight - 5);

        try {
          // 功法名称和等级
          const qualityColor = skill.getQualityColor ? skill.getQualityColor() : '#ffffff';
          this.ctx.font = '14px Arial';
          this.ctx.fillStyle = qualityColor;
          this.ctx.textAlign = 'left';
          this.ctx.fillText(
            `${skill.name} (Lv.${skill.level})`,
            x + 30,
            itemY + skillItemHeight - 10
          );

          // 功法星级
          this.ctx.fillStyle = '#ffcc00';
          this.ctx.textAlign = 'right';
          this.ctx.fillText(
            `${skill.stars}★`,
            x + width - 30,
            itemY + skillItemHeight - 10
          );
        } catch (error) {
          console.error('绘制功法列表项时出错:', error);
          // 出错时显示简单信息
          this.ctx.font = '14px Arial';
          this.ctx.fillStyle = '#ffffff';
          this.ctx.textAlign = 'left';
          this.ctx.fillText(
            skill.name || '功法',
            x + 30,
            itemY + skillItemHeight - 10
          );
        }
      }

      // 如果功法数量超过显示上限，显示更多提示
      if (skills.length > maxDisplaySkills) {
        const moreY = listY + 25 + maxDisplaySkills * skillItemHeight;

        this.ctx.font = '12px Arial';
        this.ctx.fillStyle = '#aaaaaa';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(
          `还有 ${skills.length - maxDisplaySkills} 个功法...`,
          x + width / 2,
          moreY + 10
        );
      }
    } else {
      this.ctx.font = '14px Arial';
      this.ctx.fillStyle = '#aaaaaa';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(
        '尚未学习任何功法',
        x + width / 2,
        skillSlotY + skillSlotSize + 50
      );
    }
  }

  // 绘制进阶信息
  drawAdvanceInfo(x, y, width, height) {
    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';

    // 获取玩家资源
    const gameStateManager = AppContext.game.gameStateManager;
    const player = gameStateManager.getPlayer();
    const resources = player.resources || {};
    const expPotions = resources.expPotion || 0;
    const fragments = this.character.fragments || 0;
    const currentStarCost = this.getStarUpgradeCost(this.character.star || 1);

    // 绘制角色当前信息
    this.ctx.fillText(`角色等级: ${this.character.level}`, x + 20, y + 30);
    this.ctx.fillText(`当前修为: ${this.character.cultivation}`, x + 20, y + 55);
    this.ctx.fillText(`当前星级: ${this.character.star || 1}星`, x + 20, y + 80);

    // 绘制资源信息
    this.ctx.fillText(`修为丹: ${expPotions}`, x + width / 2 + 20, y + 30);
    this.ctx.fillText(`角色碎片: ${fragments}/${currentStarCost}`, x + width / 2 + 20, y + 55);

    // 绘制升级提示
    this.ctx.fillText('点击升级按钮提升角色等级', x + 20, y + 115);
    this.ctx.fillText('点击升星按钮提升角色星级', x + 20, y + 140);

    // 如果可以升星，显示提示
    if (fragments >= currentStarCost && (this.character.star || 1) < 6) {
      this.ctx.fillStyle = '#7fff00';
      this.ctx.fillText('可以升星！提升属性加成！', x + 20, y + 170);
    }
  }
}

export default CharacterDetailScene;