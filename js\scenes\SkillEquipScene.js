/**
 * 技能装备场景
 * 用于装备和管理角色的战斗技能
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import game from '../../game';
import AppContext from '../utils/AppContext';

class SkillEquipScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager) {
    super(ctx, screenWidth, screenHeight, sceneManager);

    // 场景资源
    this.resources = null;

    // 当前角色
    this.character = null;

    // 页面状态
    this.pageTitle = '技能装备';

    // 选中的技能槽位（0-5）
    this.selectedSlotIndex = -1;

    // 选中的技能（从技能列表中）
    this.selectedSkillIndex = -1;

    // 可用技能列表
    this.availableSkills = [];

    // 拖拽状态
    this.isDragging = false;
    this.dragSkill = null;
    this.dragStartX = 0;
    this.dragStartY = 0;

    // 初始化UI
    this.initUI();
  }

  // 初始化UI
  initUI() {
    // 创建返回按钮
    const backButtonSize = 40;
    const margin = 10;

    this.backButton = new Button(
      this.ctx,
      this.screenWidth - backButtonSize - margin,
      this.screenHeight - backButtonSize - margin,
      backButtonSize,
      backButtonSize,
      '返回',
      null,
      null,
      () => {
        // 返回角色详情页
        this.sceneManager.showScene('characterDetail', { characterId: this.character.id });
      }
    );

    this.addUIElement(this.backButton);
  }

  // 场景显示时的回调
  onShow(params) {
    try {
      console.log("SkillEquipScene onShow", params);
      this.visible = true;

      // 确保能获取到游戏资源
      this.resources = game.resourceLoader.resources;

      // 清空UI元素
      this.clearUIElements();

      // 获取角色数据
      if (params && params.character) {
        this.character = params.character;
        console.log(`成功加载技能装备页，角色名称: ${this.character.name}`);
      } else {
        console.error('技能装备页缺少角色参数');
        return;
      }

      // 加载可用技能
      this.loadAvailableSkills();

      // 初始化UI
      this.initUI();

      // 重置选中状态
      this.selectedSlotIndex = -1;
      this.selectedSkillIndex = -1;
    } catch (error) {
      console.error("SkillEquipScene.onShow 出错:", error);
    }
  }

  // 加载可用技能
  loadAvailableSkills() {
    try {
      // 从技能管理器获取所有技能
      const skillManager = AppContext.game.skillManager;
      if (skillManager) {
        const allSkills = skillManager.getAllSkills();

        // 筛选出战斗技能（普通攻击和主动技能）
        this.availableSkills = allSkills.filter(skill =>
          skill.type === 'normalAttack' || skill.type === 'activeSkill'
        );

        console.log(`加载了 ${this.availableSkills.length} 个可用战斗技能`);
      } else {
        console.warn('技能管理器未初始化');
        this.availableSkills = [];
      }
    } catch (error) {
      console.error('加载可用技能时出错:', error);
      this.availableSkills = [];
    }
  }

  // 处理触摸开始事件
  handleTouchStart(x, y) {
    console.log(`SkillEquipScene handleTouchStart: ${x},${y}`);

    // 检查是否点击了技能槽位
    const slotIndex = this.getSkillSlotAtPosition(x, y);
    if (slotIndex !== -1) {
      this.selectedSlotIndex = slotIndex;
      console.log(`选中技能槽位: ${slotIndex}`);
      return true;
    }

    // 检查是否点击了技能列表中的技能
    const skillIndex = this.getSkillListItemAtPosition(x, y);
    if (skillIndex !== -1) {
      this.selectedSkillIndex = skillIndex;
      console.log(`选中技能: ${skillIndex}`);

      // 开始拖拽
      this.isDragging = true;
      this.dragSkill = this.availableSkills[skillIndex];
      this.dragStartX = x;
      this.dragStartY = y;
      return true;
    }

    return false;
  }

  // 处理触摸移动事件
  handleTouchMove(x, y) {
    if (this.isDragging) {
      // 更新拖拽位置
      return true;
    }
    return false;
  }

  // 处理触摸结束事件
  handleTouchEnd(x, y) {
    if (this.isDragging) {
      // 检查是否拖拽到技能槽位
      const slotIndex = this.getSkillSlotAtPosition(x, y);
      if (slotIndex !== -1 && this.dragSkill) {
        this.equipSkillToSlot(slotIndex, this.dragSkill);
      }

      // 结束拖拽
      this.isDragging = false;
      this.dragSkill = null;
      return true;
    }

    return false;
  }

  // 装备技能到槽位
  equipSkillToSlot(slotIndex, skill) {
    try {
      // 检查技能类型是否匹配槽位
      if (slotIndex === 0 && skill.type !== 'normalAttack') {
        console.error('普通攻击槽位只能装备普通攻击技能');
        return false;
      }

      if (slotIndex > 0 && skill.type !== 'activeSkill') {
        console.error('主动技能槽位只能装备主动技能');
        return false;
      }

      // 装备技能
      const success = this.character.equipSkillToSlot(slotIndex, skill);
      if (success) {
        console.log(`成功装备技能 ${skill.name} 到槽位 ${slotIndex}`);

        // 保存游戏状态
        game.gameStateManager.updateCharacter(this.character.id, this.character);
        game.gameStateManager.saveGameState();
      }

      return success;
    } catch (error) {
      console.error('装备技能时出错:', error);
      return false;
    }
  }

  // 获取指定位置的技能槽位
  getSkillSlotAtPosition(x, y) {
    const slotSize = 60;
    const slotSpacing = 10;
    const slotsPerRow = 6;
    const startX = (this.screenWidth - (slotSize * slotsPerRow + slotSpacing * (slotsPerRow - 1))) / 2;
    const startY = 120;

    for (let i = 0; i < 6; i++) {
      const slotX = startX + i * (slotSize + slotSpacing);
      const slotY = startY;

      if (x >= slotX && x <= slotX + slotSize && y >= slotY && y <= slotY + slotSize) {
        return i;
      }
    }

    return -1;
  }

  // 获取指定位置的技能列表项
  getSkillListItemAtPosition(x, y) {
    const listStartY = 250;
    const itemHeight = 50;
    const itemSpacing = 5;

    for (let i = 0; i < this.availableSkills.length; i++) {
      const itemY = listStartY + i * (itemHeight + itemSpacing);

      if (x >= 20 && x <= this.screenWidth - 20 &&
          y >= itemY && y <= itemY + itemHeight) {
        return i;
      }
    }

    return -1;
  }

  // 子类实现的绘制逻辑
  drawScene() {
    if (!this.character) {
      return;
    }

    // 绘制渐变背景
    this.drawGradientBackground();

    // 绘制顶部导航栏
    this.drawHeader();

    // 绘制技能槽位
    this.drawSkillSlots();

    // 绘制技能列表
    this.drawSkillList();

    // 绘制拖拽中的技能
    if (this.isDragging && this.dragSkill) {
      this.drawDraggingSkill();
    }
  }

  // 绘制渐变背景
  drawGradientBackground() {
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
    gradient.addColorStop(0, 'rgba(25, 25, 50, 0.95)');
    gradient.addColorStop(0.5, 'rgba(15, 15, 35, 0.95)');
    gradient.addColorStop(1, 'rgba(5, 5, 20, 0.95)');

    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
  }

  // 绘制顶部导航栏
  drawHeader() {
    const headerHeight = 80;

    // 绘制顶部导航栏背景
    const headerGradient = this.ctx.createLinearGradient(0, 0, 0, headerHeight);
    headerGradient.addColorStop(0, 'rgba(0, 0, 0, 0.8)');
    headerGradient.addColorStop(1, 'rgba(0, 0, 0, 0.4)');
    this.ctx.fillStyle = headerGradient;
    this.ctx.fillRect(0, 0, this.screenWidth, headerHeight);

    // 绘制页面标题
    this.ctx.font = 'bold 28px Arial';
    this.ctx.fillStyle = '#FFD700';
    this.ctx.textAlign = 'left';
    this.ctx.strokeStyle = 'rgba(0, 0, 0, 0.5)';
    this.ctx.lineWidth = 2;
    this.ctx.strokeText(this.pageTitle, 20, headerHeight / 2 + 10);
    this.ctx.fillText(this.pageTitle, 20, headerHeight / 2 + 10);

    // 绘制角色名称
    this.ctx.font = 'bold 18px Arial';
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.textAlign = 'right';
    this.ctx.fillText(`角色: ${this.character.name}`, this.screenWidth - 20, headerHeight / 2 + 6);
  }

  // 绘制技能槽位
  drawSkillSlots() {
    const slotSize = 60;
    const slotSpacing = 10;
    const slotsPerRow = 6;
    const startX = (this.screenWidth - (slotSize * slotsPerRow + slotSpacing * (slotsPerRow - 1))) / 2;
    const startY = 120;

    // 绘制槽位背景
    this.drawRoundedCard(startX - 20, startY - 20,
      slotSize * slotsPerRow + slotSpacing * (slotsPerRow - 1) + 40,
      slotSize + 80);

    // 绘制标题
    this.ctx.font = 'bold 18px Arial';
    this.ctx.fillStyle = '#FFD700';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('技能装备槽位', this.screenWidth / 2, startY - 5);

    // 槽位名称
    const slotNames = ['普通攻击', '主动技能1', '主动技能2', '主动技能3', '主动技能4', '主动技能5'];
    const slotColors = ['#FF6B6B', '#4ECDC4', '#4ECDC4', '#4ECDC4', '#4ECDC4', '#4ECDC4'];

    // 绘制6个技能槽位
    for (let i = 0; i < 6; i++) {
      const x = startX + i * (slotSize + slotSpacing);
      const y = startY;

      // 获取当前槽位的技能
      const equippedSkill = this.character.getSkillInSlot(i);

      // 绘制槽位背景
      const isSelected = this.selectedSlotIndex === i;
      this.ctx.fillStyle = isSelected ? 'rgba(255, 215, 0, 0.3)' : 'rgba(100, 100, 150, 0.6)';
      this.drawRoundedRect(x, y, slotSize, slotSize, 8);
      this.ctx.fill();

      // 绘制槽位边框
      this.ctx.strokeStyle = isSelected ? '#FFD700' : slotColors[i];
      this.ctx.lineWidth = 2;
      this.ctx.stroke();

      // 如果有装备的技能，绘制技能
      if (equippedSkill) {
        // 绘制技能名称首字母
        this.ctx.font = 'bold 20px Arial';
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText(equippedSkill.name.charAt(0), x + slotSize / 2, y + slotSize / 2);

        // 绘制技能等级
        this.ctx.font = 'bold 10px Arial';
        this.ctx.fillStyle = '#FFD700';
        this.ctx.textAlign = 'right';
        this.ctx.textBaseline = 'bottom';
        this.ctx.fillText(`Lv.${equippedSkill.level || 1}`, x + slotSize - 3, y + slotSize - 3);
      } else {
        // 绘制空槽位提示
        this.ctx.font = '24px Arial';
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText('+', x + slotSize / 2, y + slotSize / 2);
      }

      // 绘制槽位名称
      this.ctx.font = '10px Arial';
      this.ctx.fillStyle = '#FFFFFF';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'top';
      this.ctx.fillText(slotNames[i], x + slotSize / 2, y + slotSize + 5);
    }
  }

  // 绘制技能列表
  drawSkillList() {
    const listStartY = 250;
    const itemHeight = 50;
    const itemSpacing = 5;
    const listWidth = this.screenWidth - 40;

    // 绘制列表背景
    const listHeight = Math.min(this.availableSkills.length * (itemHeight + itemSpacing) + 20,
                                this.screenHeight - listStartY - 100);
    this.drawRoundedCard(20, listStartY - 20, listWidth, listHeight + 40);

    // 绘制标题
    this.ctx.font = 'bold 18px Arial';
    this.ctx.fillStyle = '#FFD700';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('可用技能', this.screenWidth / 2, listStartY - 5);

    // 绘制技能列表
    for (let i = 0; i < this.availableSkills.length; i++) {
      const skill = this.availableSkills[i];
      const itemY = listStartY + i * (itemHeight + itemSpacing);

      // 检查是否超出显示区域
      if (itemY > this.screenHeight - 150) break;

      const isSelected = this.selectedSkillIndex === i;

      // 绘制技能项背景
      this.ctx.fillStyle = isSelected ? 'rgba(255, 215, 0, 0.3)' : 'rgba(255, 255, 255, 0.1)';
      this.drawRoundedRect(30, itemY, listWidth - 20, itemHeight, 5);
      this.ctx.fill();

      // 绘制技能图标
      const iconSize = 40;
      const iconX = 40;
      const iconY = itemY + 5;

      // 根据技能类型设置颜色
      const typeColors = {
        'normalAttack': '#FF6B6B',
        'activeSkill': '#4ECDC4'
      };
      const typeColor = typeColors[skill.type] || '#FFFFFF';

      this.ctx.fillStyle = typeColor;
      this.drawRoundedRect(iconX, iconY, iconSize, iconSize, 5);
      this.ctx.fill();

      // 绘制技能名称首字母
      this.ctx.font = 'bold 18px Arial';
      this.ctx.fillStyle = '#FFFFFF';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
      this.ctx.fillText(skill.name.charAt(0), iconX + iconSize / 2, iconY + iconSize / 2);

      // 绘制技能信息
      const textX = iconX + iconSize + 10;

      // 技能名称
      this.ctx.font = 'bold 16px Arial';
      this.ctx.fillStyle = '#FFFFFF';
      this.ctx.textAlign = 'left';
      this.ctx.textBaseline = 'top';
      this.ctx.fillText(skill.name, textX, itemY + 5);

      // 技能类型
      this.ctx.font = '12px Arial';
      this.ctx.fillStyle = typeColor;
      const typeText = skill.type === 'normalAttack' ? '普通攻击' : '主动技能';
      this.ctx.fillText(typeText, textX, itemY + 25);

      // 技能描述（简短版）
      this.ctx.font = '10px Arial';
      this.ctx.fillStyle = '#CCCCCC';
      const shortDesc = skill.description.length > 20 ?
        skill.description.substring(0, 20) + '...' : skill.description;
      this.ctx.fillText(shortDesc, textX + 80, itemY + 25);
    }
  }

  // 绘制拖拽中的技能
  drawDraggingSkill() {
    if (!this.dragSkill) return;

    // 获取当前鼠标位置（这里简化处理）
    const x = this.dragStartX;
    const y = this.dragStartY;

    // 绘制半透明的技能图标
    this.ctx.globalAlpha = 0.7;

    const iconSize = 40;
    this.ctx.fillStyle = '#4ECDC4';
    this.drawRoundedRect(x - iconSize / 2, y - iconSize / 2, iconSize, iconSize, 5);
    this.ctx.fill();

    this.ctx.font = 'bold 16px Arial';
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(this.dragSkill.name.charAt(0), x, y);

    this.ctx.globalAlpha = 1.0;
  }

  // 绘制圆角矩形的辅助方法
  drawRoundedRect(x, y, width, height, radius) {
    this.ctx.beginPath();
    this.ctx.moveTo(x + radius, y);
    this.ctx.lineTo(x + width - radius, y);
    this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    this.ctx.lineTo(x + width, y + height - radius);
    this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    this.ctx.lineTo(x + radius, y + height);
    this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    this.ctx.lineTo(x, y + radius);
    this.ctx.quadraticCurveTo(x, y, x + radius, y);
    this.ctx.closePath();
  }

  // 绘制圆角卡片
  drawRoundedCard(x, y, width, height) {
    const radius = 15;

    // 绘制阴影
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    this.drawRoundedRect(x + 3, y + 3, width, height, radius);
    this.ctx.fill();

    // 绘制卡片背景渐变
    const cardGradient = this.ctx.createLinearGradient(x, y, x, y + height);
    cardGradient.addColorStop(0, 'rgba(40, 40, 80, 0.9)');
    cardGradient.addColorStop(1, 'rgba(20, 20, 40, 0.9)');

    this.ctx.fillStyle = cardGradient;
    this.drawRoundedRect(x, y, width, height, radius);
    this.ctx.fill();

    // 绘制卡片边框
    this.ctx.strokeStyle = 'rgba(255, 215, 0, 0.6)';
    this.ctx.lineWidth = 2;
    this.ctx.stroke();
  }
}

export default SkillEquipScene;
