/**
 * 主线关卡场景类
 * 主线游戏关卡，共10关，每关10层
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import game from '../../game';
import BattleManager from '../battle/BattleManager';

class StoryScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager, resources) {
    super(ctx, screenWidth, screenHeight, sceneManager);

    // 场景资源
    this.resources = resources || {};

    // 当前选中的底部导航项
    this.selectedTabIndex = 0; // 主页对应的导航索引

    // 主线关卡配置
    this.chapters = Array.from({ length: 10 }, (_, chapterIndex) => {
      return {
        id: chapterIndex + 1,
        name: `第${chapterIndex + 1}关`,
        levels: Array.from({ length: 10 }, (_, levelIndex) => {
          return {
            id: levelIndex + 1,
            name: `第${levelIndex + 1}层`,
            enemyLevel: chapterIndex * 10 + levelIndex + 1,
            reward: {
              lingshi: (chapterIndex + 1) * 200 + (levelIndex + 1) * 20,
              exp: (chapterIndex + 1) * 100 + (levelIndex + 1) * 10,
              xianyu: Math.floor((chapterIndex + 1) / 2) // 每两关奖励一个仙玉
            }
          };
        })
      };
    });

    // 当前选中的章节
    this.currentChapter = null;

    // 返回按钮
    this.backButton = null;

    // 是否正在显示关卡列表（第二层）
    this.showingLevels = false;
  }

  // 初始化UI
  initUI() {
    // 清空UI元素
    this.clearUIElements();

    // 创建底部导航栏按钮
    this.createTabButtons();

    // 创建返回按钮
    this.createBackButton();

    // 根据当前状态创建章节或关卡按钮
    if (this.showingLevels && this.currentChapter) {
      // 显示特定章节的关卡列表
      this.createLevelButtons(this.chapters.find(c => c.id === this.currentChapter));
    } else {
      // 显示章节列表
      this.createChapterButtons();
      this.currentChapter = null;
    }
  }

  // 创建底部导航栏按钮
  createTabButtons() {
    const tabBarHeight = 60;
    const tabBarY = this.screenHeight - tabBarHeight;
    const tabButtonWidth = this.screenWidth / 5;

    // 创建底部导航栏按钮
    this.tabButtons = [
      this.createTabButton(0 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '主页', 0),
      this.createTabButton(1 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '角色', 1),
      this.createTabButton(2 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '洞府', 2),
      this.createTabButton(3 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '试炼', 3),
      this.createTabButton(4 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '背包', 4)
    ];

    // 将按钮添加到UI元素列表
    this.tabButtons.forEach(button => {
      this.addUIElement(button);
    });
  }

  // 创建返回按钮
  createBackButton() {
    const buttonWidth = 80;
    const buttonHeight = 40;
    const margin = 10;

    this.backButton = new Button(
      this.ctx,
      margin,
      margin,
      buttonWidth,
      buttonHeight,
      '返回',
      null,
      null,
      () => {
        console.log('点击返回按钮');
        if (this.showingLevels) {
          // 如果正在显示关卡列表，返回到章节列表
          this.showingLevels = false;
          this.currentChapter = null;
          this.clearUIElements();
          this.initUI();
        } else {
          // 否则返回到主页面，携带from参数，指示来源场景
          this.sceneManager.showScene('main', { from: 'story' });
        }
      }
    );

    this.addUIElement(this.backButton);
  }

  // 创建章节按钮
  createChapterButtons() {
    const headerHeight = 80;
    const topMargin = 140;
    const chapterButtonWidth = this.screenWidth * 0.8;
    const chapterButtonHeight = 50;
    const chapterMargin = 20;

    // 清除之前的按钮
    if (this.chapterButtons) {
      this.chapterButtons.forEach(button => {
        this.removeUIElement(button);
      });
    }

    if (this.levelButtons) {
      this.levelButtons.forEach(button => {
        this.removeUIElement(button);
      });
      this.levelButtons = [];
    }

    // 创建章节按钮
    this.chapterButtons = [];
    for (let i = 0; i < this.chapters.length; i++) {
      const chapter = this.chapters[i];

      // 获取该章节最高已通关层数，确定按钮是否可用
      const progress = this.getChapterProgress(chapter.id);
      // 第一关始终解锁，其他关卡需要前一关通关所有层数
      const isUnlocked = i === 0 || (i > 0 && this.getChapterProgress(i) > 0);

      const button = new Button(
        this.ctx,
        (this.screenWidth - chapterButtonWidth) / 2,
        topMargin + i * (chapterButtonHeight + chapterMargin),
        chapterButtonWidth,
        chapterButtonHeight,
        `${chapter.name} ${progress > 0 ? '(已通关: ' + progress + '层)' : ''}`,
        null,
        isUnlocked ? null : 'rgba(100, 100, 100, 0.7)',
        () => {
          if (isUnlocked) {
            // 选中章节，显示关卡列表
            this.selectChapter(chapter.id);
          } else {
            wx.showToast({
              title: '请先通关前一关卡',
              icon: 'none',
              duration: 2000
            });
          }
        }
      );

      this.chapterButtons.push(button);
      this.addUIElement(button);
    }
  }

  // 选择章节，显示关卡列表
  selectChapter(chapterId) {
    this.currentChapter = chapterId;
    this.showingLevels = true;

    // 清空UI元素并重新初始化，显示关卡列表
    this.clearUIElements();
    this.initUI();
  }

  // 创建指定章节的关卡层数按钮
  createLevelButtons(chapter) {
    if (!chapter) return;

    const headerHeight = 80;
    const levelStartY = 140;
    const levelButtonWidth = this.screenWidth * 0.8;
    const levelButtonHeight = 50;
    const levelMargin = 15;

    this.levelButtons = [];

    // 获取当前章节最高已通关层数
    const chapterProgress = this.getChapterProgress(chapter.id);

    for (let i = 0; i < chapter.levels.length; i++) {
      const level = chapter.levels[i];
      // 第一层始终解锁，其他层数需要前一层通关
      const isUnlocked = i === 0 || i <= chapterProgress;

      const button = new Button(
        this.ctx,
        (this.screenWidth - levelButtonWidth) / 2,
        levelStartY + i * (levelButtonHeight + levelMargin),
        levelButtonWidth,
        levelButtonHeight,
        `${level.name} ${isUnlocked ? '' : '(未解锁)'}`,
        null,
        isUnlocked ? null : 'rgba(100, 100, 100, 0.7)',
        () => {
          if (isUnlocked) {
            this.startLevel(chapter.id, level.id);
          } else {
            wx.showToast({
              title: '请先通关前一层',
              icon: 'none',
              duration: 2000
            });
          }
        }
      );

      this.levelButtons.push(button);
      this.addUIElement(button);
    }
  }

  // 创建底部导航栏按钮
  createTabButton(x, y, width, height, text, index) {
    return new Button(
      this.ctx,
      x,
      y,
      width,
      height,
      text,
      null,
      null,
      () => {
        this.onTabSelected(index);
      }
    );
  }

  // 底部导航栏选中回调
  onTabSelected(index) {
    // 如果点击的是当前选中的项，不做处理
    if (this.selectedTabIndex === index) {
      return;
    }

    // 更新选中的索引
    this.selectedTabIndex = index;

    // 根据索引切换场景
    switch (index) {
      case 0:
        // 主页
        this.sceneManager.showScene('main', { from: 'story' });
        break;
      case 1:
        // 角色页面
        this.sceneManager.showScene('character');
        break;
      case 2:
        // 洞府页面
        this.sceneManager.showScene('dongfu');
        break;
      case 3:
        // 试炼页面
        this.sceneManager.showScene('trial');
        break;
      case 4:
        // 背包页面
        this.sceneManager.showScene('backpack');
        break;
    }
  }

  // 获取章节进度(最高已通关层数)
  getChapterProgress(chapterId) {
    if (!game.gameStateManager) return 0;

    const player = game.gameStateManager.getPlayer();
    if (!player) return 0;

    // 确保存在storyProgress对象
    if (!player.storyProgress) {
      player.storyProgress = {};
    }

    // 返回章节进度，未设置则返回0
    return player.storyProgress[`chapter_${chapterId}`] || 0;
  }

  // 开始关卡
  startLevel(chapterId, levelId) {
    console.log(`开始主线关卡 ${chapterId}-${levelId}`);

    const chapter = this.chapters.find(c => c.id === chapterId);
    if (!chapter) return;

    const level = chapter.levels.find(l => l.id === levelId);
    if (!level) return;

    // 获取角色列表
    const characters = game.gameStateManager.getCharacters();
    if (characters.length === 0) {
      console.log('没有可用角色，无法进行挑战！');
      wx.showToast({
        title: '没有可用角色',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 创建敌人
    const enemy = {
      id: 2000 + chapterId * 100 + levelId,
      name: `主线敌人 ${chapterId}-${levelId}`,
      level: level.enemyLevel,
      maxHp: 100 + level.enemyLevel * 10,
      hp: 100 + level.enemyLevel * 10,
      attack: 10 + level.enemyLevel * 2,
      defense: 5 + level.enemyLevel,
      speed: 5 + Math.floor(level.enemyLevel / 2),
      critical: 0.05,
      criticalDamage: 1.5,
      type: 'enemy',
      skills: [],
      isAlive: function() {
        return this.hp > 0;
      }
    };

    // 创建敌方阵容
    const enemyFormation = [enemy];

    // 创建关卡数据
    const stageData = {
      id: `${chapterId}-${levelId}`,
      name: `${chapter.name} ${level.name}`,
      level: level.enemyLevel,
      type: 'story',
      chapter: chapterId,
      level: levelId,
      rewards: level.reward
    };

    // 启动布阵界面，传递敌方阵容和关卡数据
    this.sceneManager.showScene('battleFormation', {
      enemyFormation: enemyFormation,
      stageData: stageData,
      onFormationComplete: (selectedCharacters) => {
        // 用户完成布阵后开始战斗
        this.startBattle(selectedCharacters, enemyFormation, chapter, level, stageData);
      }
    });
  }

  // 开始战斗
  startBattle(playerUnits, enemyUnits, chapter, level, stageData) {
    // 确保playerUnits为数组且有战斗所需的属性
    playerUnits = playerUnits || [];

    // 获取玩家单位战斗属性
    playerUnits = playerUnits.map(unit => {
      if (typeof unit.getBattleStats !== 'function') {
        console.error(`错误：角色对象 ${unit.name} 没有 getBattleStats 方法！使用默认值`);
        return {
          id: unit.id || 'unknown',
          name: unit.name || 'Unknown Player',
          level: unit.level || 1,
          hp: 100, maxHp: 100, attack: 10, defense: 5, speed: 5,
          type: 'player',
          formationPosition: unit.formationPosition !== undefined ? unit.formationPosition : 0,
          error: 'Missing getBattleStats'
        };
      }
      // 获取包含所有加成的最终战斗属性
      const battleStats = unit.getBattleStats();

      // 保留从布阵界面传来的 formationPosition
      battleStats.formationPosition = unit.formationPosition !== undefined ? unit.formationPosition : 0;

      return battleStats;
    });

    // 如果还没有创建战斗管理器，先创建一个
    if (!game.battleManager) {
      console.log('创建战斗管理器');
      game.battleManager = new BattleManager();
    }

    // 开始战斗
    game.battleManager.startBattle({
      playerUnits: playerUnits,
      enemyUnits: enemyUnits,
      battleType: 'story',
      battleConfig: {
        stageId: stageData.id,
        stageName: stageData.name,
        chapterId: chapter.id,
        levelId: level.id
      },
      onComplete: (result) => {
        // 战斗完成后的回调
        console.log('战斗完成，结果：', result);

        // 处理战斗结果
        if (result === 'victory' || (result && result.victory === true)) {
          console.log('战斗胜利，开始处理关卡进度');

          // 更新关卡进度
          this.updateLevelProgress(chapter.id, level.id);

          // 显示战斗结果界面
          this.sceneManager.showScene('battleResult', {
            result: { victory: true },
            rewards: level.reward,
            onComplete: () => {
              // 关闭结果界面后返回主线关卡界面
              this.sceneManager.showScene('story');
            }
          });
        } else {
          // 战斗失败，直接返回
          console.log('战斗失败，返回主线关卡界面');
          this.sceneManager.showScene('story');

          wx.showToast({
            title: '挑战失败',
            icon: 'none',
            duration: 2000
          });
        }
      }
    });
  }

  // 更新关卡进度
  updateLevelProgress(chapterId, levelId) {
    if (!game.gameStateManager) return;

    const player = game.gameStateManager.getPlayer();
    if (!player) return;

    // 确保存在storyProgress对象
    if (!player.storyProgress) {
      player.storyProgress = {};
    }

    // 当前章节的进度
    const currentProgress = player.storyProgress[`chapter_${chapterId}`] || 0;

    // 更新当前章节进度
    if (levelId > currentProgress) {
      player.storyProgress[`chapter_${chapterId}`] = levelId;
      console.log(`更新章节${chapterId}进度为${levelId}`);

      // 如果完成了所有层数，解锁下一章节
      if (levelId >= 10) {
        // 确保下一章节存在
        if (chapterId < this.chapters.length) {
          // 将下一章节的进度设置为0，表示已解锁但未通关任何层数
          if (!player.storyProgress[`chapter_${chapterId + 1}`]) {
            player.storyProgress[`chapter_${chapterId + 1}`] = 0;
            console.log(`解锁章节${chapterId + 1}`);
          }
        }
      }

      // 保存游戏状态
      game.gameStateManager.setPlayer(player);
      game.gameStateManager.saveGameState();
    }
  }

  // 场景显示回调
  onShow(params) {
    // 重置状态，默认显示章节列表
    if (params && params.showLevels) {
      // 如果有明确的参数指示显示关卡列表，则保持当前状态
    } else {
      this.showingLevels = false;
      this.currentChapter = null;
    }

    // 清空UI元素
    this.clearUIElements();

    // 初始化UI
    this.initUI();

    // 更新选中的导航项
    this.selectedTabIndex = 0;
  }

  // 绘制场景
  drawScene() {
    // 绘制背景
    this.drawBackground();

    // 绘制顶部导航栏
    this.drawHeader();

    // 根据当前状态绘制不同内容
    if (this.showingLevels && this.currentChapter) {
      // 绘制关卡信息
      this.drawLevelInfo();
    } else {
      // 绘制章节选择区域
      this.drawChapterSelection();
    }

    // 绘制底部导航栏
    this.drawTabBar();
  }

  // 绘制背景
  drawBackground() {
    // 使用渐变色背景
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
    gradient.addColorStop(0, '#1a237e');
    gradient.addColorStop(1, '#000000');

    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
  }

  // 绘制顶部导航栏
  drawHeader() {
    const headerHeight = 80;

    // 绘制顶部导航栏背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, 0, this.screenWidth, headerHeight);

    // 绘制标题
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';

    if (this.showingLevels && this.currentChapter) {
      const chapter = this.chapters.find(c => c.id === this.currentChapter);
      this.ctx.fillText(`${chapter ? chapter.name : '修仙历练'}`, this.screenWidth / 2, headerHeight / 2);
    } else {
      this.ctx.fillText('修仙历练', this.screenWidth / 2, headerHeight / 2);
    }
  }

  // 绘制章节选择区域
  drawChapterSelection() {
    const headerHeight = 80;

    // 绘制章节选择标题
    this.ctx.font = 'bold 18px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText('选择章节', this.screenWidth / 2, headerHeight + 30);
  }

  // 绘制当前章节的关卡信息
  drawLevelInfo() {
    const chapter = this.chapters.find(c => c.id === this.currentChapter);
    if (!chapter) return;

    // 绘制当前选中章节标题
    this.ctx.font = 'bold 18px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(`选择关卡`, this.screenWidth / 2, 120);
  }

  // 绘制底部导航栏
  drawTabBar() {
    const tabBarHeight = 60;
    const tabBarY = this.screenHeight - tabBarHeight;

    // 绘制底部导航栏背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, tabBarY, this.screenWidth, tabBarHeight);

    // 绘制分割线
    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
    this.ctx.lineWidth = 1;

    for (let i = 1; i < 5; i++) {
      const x = this.screenWidth * i / 5;
      this.ctx.beginPath();
      this.ctx.moveTo(x, tabBarY);
      this.ctx.lineTo(x, this.screenHeight);
      this.ctx.stroke();
    }

    // 绘制选中项的高亮背景
    const tabWidth = this.screenWidth / 5;
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
    this.ctx.fillRect(this.selectedTabIndex * tabWidth, tabBarY, tabWidth, tabBarHeight);

    // 导航栏文字
    const tabTexts = ['主页', '角色', '洞府', '试炼', '背包'];

    // 绘制导航栏图标和文字
    for (let i = 0; i < 5; i++) {
      const tabX = i * tabWidth + tabWidth / 2;
      const iconY = tabBarY + 15;

      // 绘制文字
      this.ctx.font = '14px Arial';
      this.ctx.fillStyle = i === this.selectedTabIndex ? '#ffffff' : '#a0a0a0';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
      this.ctx.fillText(tabTexts[i], tabX, tabBarY + 30);
    }
  }
}

// 使用CommonJS格式导出，确保与项目导入方式兼容
module.exports = StoryScene;