/**
 * 功法类
 * 定义游戏中的功法系统
 */
class Skill {
  /**
   * 创建一个功法
   * @param {Object} options - 功法配置选项
   */
  constructor(options = {}) {
    // 功法基本信息
    this.id = options.id || this.generateId();
    this.name = options.name || '未命名功法';
    this.level = options.level || 1;
    this.quality = options.quality || 'common';
    this.stars = options.stars || 1;
    this.maxStars = options.maxStars || 5;
    this.description = options.description || '暂无描述';
    this.type = options.type || 'passive'; // 'passive', 'normalAttack', 'activeSkill'
    this.equipped = options.equipped || false;
    this.equippedBy = options.equippedBy || null;

    // 功法进阶相关
    this.advancementLevel = options.advancementLevel || 0; // 进阶等级，0表示未进阶
    this.maxAdvancementLevel = options.maxAdvancementLevel || 3; // 最大进阶等级
    this.advancementEffects = options.advancementEffects || []; // 进阶效果

    // 特殊技能效果
    this.specialEffects = options.specialEffects || []; // 特殊效果列表

    // 战斗相关属性（仅对战斗技能有效）
    this.castTime = options.castTime || 0; // 吟唱时间（毫秒）
    this.cooldown = options.cooldown || 0; // 冷却时间（毫秒）
    this.damage = options.damage || 0; // 技能伤害
    this.damageType = options.damageType || 'physical'; // 伤害类型：'physical', 'magical'
    this.targetType = options.targetType || 'enemy'; // 目标类型：'enemy', 'self', 'ally'
    this.animationType = options.animationType || 'melee'; // 动画类型：'melee', 'ranged', 'magic'

    // 功法属性加成
    this.attributes = {
      hp: options.attributes?.hp || 0,
      hpPercent: options.attributes?.hpPercent || 0,
      attack: options.attributes?.attack || 0,
      attackPercent: options.attributes?.attackPercent || 0,
      defense: options.attributes?.defense || 0,
      defensePercent: options.attributes?.defensePercent || 0,
      speed: options.attributes?.speed || 0,
      critical: options.attributes?.critical || 0,
      critDamage: options.attributes?.critDamage || 0
    };

    // 装备限制
    this.restriction = options.restriction || { minLevel: 1 };

    // 计算功法强度
    this.power = this.calculatePower();
  }

  /**
   * 生成唯一ID
   * @returns {string} 生成的唯一ID
   */
  generateId() {
    return 'skill_' + Date.now() + '_' + Math.floor(Math.random() * 1000);
  }

  /**
   * 获取功法品质的中文名称
   * @returns {string} 功法品质的中文名称
   */
  getQualityName() {
    const qualityMap = {
      'common': '普通',
      'uncommon': '优秀',
      'rare': '稀有',
      'epic': '史诗',
      'legendary': '传说',
      'mythic': '神话'
    };

    return qualityMap[this.quality] || '未知';
  }

  /**
   * 获取功法品质对应的颜色
   * @returns {string} 品质对应的十六进制颜色值
   */
  getQualityColor() {
    const colorMap = {
      'common': '#a0a0a0',
      'uncommon': '#00cc00',
      'rare': '#0070dd',
      'epic': '#a335ee',
      'legendary': '#ff8000',
      'mythic': '#ff0000'
    };

    return colorMap[this.quality] || '#ffffff';
  }

  /**
   * 计算功法强度
   * @returns {number} 功法强度
   */
  calculatePower() {
    let power = 0;
    const qualityMultiplier = {
      'common': 1,
      'uncommon': 1.2,
      'rare': 1.5,
      'epic': 2,
      'legendary': 3,
      'mythic': 5
    };

    // 基础属性加成
    if (this.attributes) {
      if (this.attributes.hp) power += this.attributes.hp * 0.1;
      if (this.attributes.hpPercent) power += this.attributes.hpPercent * 5;
      if (this.attributes.attack) power += this.attributes.attack;
      if (this.attributes.attackPercent) power += this.attributes.attackPercent * 10;
      if (this.attributes.defense) power += this.attributes.defense * 0.7;
      if (this.attributes.defensePercent) power += this.attributes.defensePercent * 7;
    }

    // 乘以品质和星级修正
    power *= qualityMultiplier[this.quality] || 1;
    power *= (1 + (this.level - 1) * 0.1);
    power *= (1 + (this.stars - 1) * 0.2);

    // 进阶加成
    power *= (1 + this.advancementLevel * 0.3);

    // 特殊效果加成
    if (this.specialEffects && this.specialEffects.length > 0) {
      // 每个特殊效果增加5%战力
      power *= (1 + this.specialEffects.length * 0.05);
    }

    this.power = Math.floor(power);
    return this.power;
  }

  /**
   * 获取功法属性文本描述
   * @returns {string} 功法属性的文本描述
   */
  getAttributesDescription() {
    const attrs = this.attributes;
    const descriptions = [];

    if (attrs.hp > 0) {
      descriptions.push(`增加生命值: ${attrs.hp}`);
    }

    if (attrs.hpPercent > 0) {
      descriptions.push(`增加生命值: ${attrs.hpPercent}%`);
    }

    if (attrs.attack > 0) {
      descriptions.push(`增加攻击力: ${attrs.attack}`);
    }

    if (attrs.attackPercent > 0) {
      descriptions.push(`增加攻击力: ${attrs.attackPercent}%`);
    }

    if (attrs.defense > 0) {
      descriptions.push(`增加防御力: ${attrs.defense}`);
    }

    if (attrs.defensePercent > 0) {
      descriptions.push(`增加防御力: ${attrs.defensePercent}%`);
    }

    if (attrs.speed > 0) {
      descriptions.push(`增加速度: ${attrs.speed}`);
    }

    if (attrs.critical > 0) {
      descriptions.push(`增加暴击率: ${attrs.critical}%`);
    }

    if (attrs.critDamage > 0) {
      descriptions.push(`增加暴击伤害: ${attrs.critDamage}%`);
    }

    // 添加进阶信息
    if (this.advancementLevel > 0) {
      descriptions.push(`\n进阶等级: ${this.advancementLevel}/${this.maxAdvancementLevel}`);

      // 添加进阶效果描述
      if (this.advancementEffects && this.advancementEffects.length > 0) {
        for (let i = 0; i < this.advancementLevel && i < this.advancementEffects.length; i++) {
          const effect = this.advancementEffects[i];
          descriptions.push(`进阶${i+1}效果: ${effect.description}`);
        }
      }
    }

    // 添加特殊效果描述
    if (this.specialEffects && this.specialEffects.length > 0) {
      descriptions.push('\n特殊效果:');
      this.specialEffects.forEach(effect => {
        descriptions.push(`- ${effect.name}: ${effect.description}`);
      });
    }

    return descriptions.join('\n');
  }

  /**
   * 检查角色是否满足装备此功法的条件
   * @param {Object} character 角色对象
   * @returns {boolean} 是否满足条件
   */
  canEquip(character) {
    if (!character) return false;
    if (!this.restriction) return true;

    // 检查等级限制
    if (this.restriction.minLevel && character.level < this.restriction.minLevel) {
      return false;
    }

    // 检查修为限制
    if (this.restriction.cultivation && character.cultivation !== this.restriction.cultivation) {
      return false;
    }

    // 检查性别限制
    if (this.restriction.gender && character.gender !== this.restriction.gender) {
      return false;
    }

    return true;
  }

  /**
   * 装备到指定角色
   * @param {Character} character 要装备的角色
   * @returns {boolean} 是否装备成功
   */
  equip(character) {
    if (!this.canEquip(character)) {
      return false;
    }

    this.equipped = true;
    this.equippedBy = character.id;
    return true;
  }

  /**
   * 取消装备
   */
  unequip() {
    this.equipped = false;
    this.equippedBy = null;
  }

  /**
   * 获取升级所需材料
   * @returns {Object} 升级所需材料
   */
  getUpgradeMaterials() {
    // 根据品质和等级计算升级所需的材料
    const qualityMultiplier = {
      'common': 1,
      'uncommon': 1.5,
      'rare': 2,
      'epic': 3,
      'legendary': 5,
      'mythic': 10
    };

    const baseAmount = 10;
    const levelMultiplier = 1 + (this.level - 1) * 0.5;
    const amount = Math.ceil(baseAmount * (qualityMultiplier[this.quality] || 1) * levelMultiplier);

    return {
      'skill_essence': amount
    };
  }

  /**
   * 获取升星所需碎片
   * @returns {Object} 升星所需碎片
   */
  getStarUpgradeMaterials() {
    // 检查是否已经达到最大星级
    if (this.stars >= this.maxStars) {
      return null;
    }

    // 根据品质和当前星级计算升星所需的碎片数量
    const qualityMap = {
      'common': 'common_fragment',
      'uncommon': 'uncommon_fragment',
      'rare': 'rare_fragment',
      'epic': 'epic_fragment',
      'legendary': 'legendary_fragment',
      'mythic': 'mythic_fragment'
    };

    const fragmentId = qualityMap[this.quality] || 'common_fragment';
    const baseAmount = 5;
    const starMultiplier = this.stars * 2;
    const amount = baseAmount * starMultiplier;

    const result = {};
    result[fragmentId] = amount;
    return result;
  }

  /**
   * 升级功法
   * @param {number} levels 升级的级数
   * @returns {boolean} 是否升级成功
   */
  upgrade(levels = 1) {
    if (levels <= 0) return false;

    this.level += levels;
    // 重新计算战力
    this.power = this.calculatePower();
    return true;
  }

  /**
   * 功法进阶
   * @returns {Object} 进阶结果，包含成功与否和解锁的效果
   */
  advance() {
    // 检查是否已达到最大进阶等级
    if (this.advancementLevel >= this.maxAdvancementLevel) {
      return { success: false, reason: '已达到最大进阶等级' };
    }

    // 检查功法等级是否足够
    const requiredLevel = 10 + this.advancementLevel * 10;
    if (this.level < requiredLevel) {
      return { success: false, reason: `功法等级不足，需要达到${requiredLevel}级` };
    }

    // 进阶成功
    this.advancementLevel += 1;

    // 获取新解锁的进阶效果
    let unlockedEffect = null;
    if (this.advancementEffects && this.advancementLevel <= this.advancementEffects.length) {
      unlockedEffect = this.advancementEffects[this.advancementLevel - 1];

      // 应用进阶效果到属性
      if (unlockedEffect.attributes) {
        for (const attr in unlockedEffect.attributes) {
          if (this.attributes[attr] !== undefined) {
            this.attributes[attr] += unlockedEffect.attributes[attr];
          }
        }
      }

      // 如果有特殊效果，添加到功法特殊效果列表
      if (unlockedEffect.specialEffect) {
        if (!this.specialEffects) this.specialEffects = [];
        this.specialEffects.push(unlockedEffect.specialEffect);
      }
    }

    // 重新计算战力
    this.power = this.calculatePower();

    return {
      success: true,
      newLevel: this.advancementLevel,
      unlockedEffect: unlockedEffect
    };
  }

  /**
   * 升级功法星级
   * @returns {boolean} 是否提升成功
   */
  upgradeStar() {
    if (this.stars >= this.maxStars) return false;

    this.stars += 1;
    // 重新计算战力
    this.power = this.calculatePower();
    return true;
  }

  /**
   * 获取进阶所需材料
   * @returns {Object} 进阶所需材料
   */
  getAdvancementMaterials() {
    // 检查是否已达到最大进阶等级
    if (this.advancementLevel >= this.maxAdvancementLevel) {
      return null;
    }

    // 根据品质和当前进阶等级计算所需材料
    const qualityMultiplier = {
      'common': 1,
      'uncommon': 2,
      'rare': 3,
      'epic': 5,
      'legendary': 8,
      'mythic': 12
    };

    // 进阶所需的必要材料
    const materials = {};

    // 必需的功法精华
    const essenceBase = 50;
    const essenceAmount = essenceBase * (this.advancementLevel + 1) * (qualityMultiplier[this.quality] || 1);
    materials['skill_essence'] = Math.floor(essenceAmount);

    // 必需的功法碎片
    const fragmentBase = 20;
    const fragmentAmount = fragmentBase * (this.advancementLevel + 1) * (qualityMultiplier[this.quality] || 1);

    // 根据品质确定碎片类型
    const qualityMap = {
      'common': 'common_fragment',
      'uncommon': 'uncommon_fragment',
      'rare': 'rare_fragment',
      'epic': 'epic_fragment',
      'legendary': 'legendary_fragment',
      'mythic': 'mythic_fragment'
    };

    const fragmentId = qualityMap[this.quality] || 'common_fragment';
    materials[fragmentId] = Math.floor(fragmentAmount);

    // 特殊材料（只有高级别进阶才需要）
    if (this.advancementLevel >= 1) {
      materials['advancement_crystal'] = this.advancementLevel * 5;
    }

    return materials;
  }

  /**
   * 获取默认的进阶效果
   * @returns {Array} 默认进阶效果列表
   */
  getDefaultAdvancementEffects() {
    // 根据功法类型和品质生成默认的进阶效果
    const effects = [];

    // 第一阶进阶：属性加成
    effects.push({
      level: 1,
      description: '属性加成提升30%',
      attributes: {
        hp: 100,
        attack: 20,
        defense: 15
      }
    });

    // 第二阶进阶：百分比加成
    effects.push({
      level: 2,
      description: '百分比属性加成',
      attributes: {
        hpPercent: 10,
        attackPercent: 10,
        defensePercent: 10
      }
    });

    // 第三阶进阶：特殊效果
    effects.push({
      level: 3,
      description: '解锁特殊效果',
      specialEffect: {
        name: '元素亲和',
        description: '每次攻击有概率触发元素亲和效果，造成额外伤害',
        triggerRate: 0.15,
        damageBonus: 0.3
      }
    });

    return effects;
  }

  /**
   * 将功法转为简单对象，用于存储
   * @returns {Object} 功法的简单对象表示
   */
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      level: this.level,
      quality: this.quality,
      stars: this.stars,
      maxStars: this.maxStars,
      description: this.description,
      type: this.type,
      equipped: this.equipped,
      equippedBy: this.equippedBy,
      restriction: this.restriction,
      attributes: this.attributes,
      power: this.power,
      advancementLevel: this.advancementLevel,
      maxAdvancementLevel: this.maxAdvancementLevel,
      advancementEffects: this.advancementEffects,
      specialEffects: this.specialEffects
    };
  }

  /**
   * 从JSON对象创建功法实例
   * @param {Object} data JSON对象
   * @returns {Skill} 功法实例
   */
  static fromJSON(data) {
    return new Skill(data);
  }
}

export default Skill;