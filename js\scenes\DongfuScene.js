/**
 * 洞府场景类
 * 展示玩家洞府，提供升级和管理功能
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import game from '../../game';

class DongfuScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager,resources) {
    super(ctx, screenWidth, screenHeight, sceneManager);

    // 场景资源
    this.resources = resources || {};

    // 当前选中的底部导航项
    this.selectedTabIndex = 2; // 洞府对应的导航索引

    // 移除在构造函数中对initUI的调用
    // 初始化UI将在onShow方法中调用
  }

  // 初始化UI
  initUI() {

    // 底部导航栏按钮
    const tabBarHeight = 60;
    const tabBarY = this.screenHeight - tabBarHeight;
    const tabButtonWidth = this.screenWidth / 5;

    // 创建底部导航栏按钮
    this.tabButtons = [
      this.createTabButton(0 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '主页', 0),
      this.createTabButton(1 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '角色', 1),
      this.createTabButton(2 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '洞府', 2),
      this.createTabButton(3 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '试炼', 3),
      this.createTabButton(4 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '背包', 4)
    ];

    // 将按钮添加到UI元素列表
    this.tabButtons.forEach(button => {
      this.addUIElement(button);
    });

    // 创建升级洞府按钮
    const upgradeButtonWidth = 150;
    const upgradeButtonHeight = 50;
    const upgradeButtonX = (this.screenWidth - upgradeButtonWidth) / 2;
    const upgradeButtonY = this.screenHeight - tabBarHeight - upgradeButtonHeight - 20;

    this.upgradeButton = new Button(
      this.ctx,
      upgradeButtonX,
      upgradeButtonY,
      upgradeButtonWidth,
      upgradeButtonHeight,
      '升级洞府',
      null,
      null,
      () => {
        this.upgradeDongfu();
      }
    );

    this.addUIElement(this.upgradeButton);

    // 创建静室按钮
    const buttonWidth = 120;
    const buttonHeight = 50;
    const buttonMargin = 20;

    this.jingshiButton = new Button(
      this.ctx,
      (this.screenWidth / 2) - buttonWidth - buttonMargin,
      this.screenHeight / 2 - buttonHeight / 2,
      buttonWidth,
      buttonHeight,
      '静室',
      null,
      null,
      () => {
        this.enterJingshi();
      }
    );

    this.addUIElement(this.jingshiButton);

    // 创建丹房按钮
    this.danfangButton = new Button(
      this.ctx,
      (this.screenWidth / 2) + buttonMargin,
      this.screenHeight / 2 - buttonHeight / 2,
      buttonWidth,
      buttonHeight,
      '丹房',
      null,
      null,
      () => {
        this.enterDanfang();
      }
    );

    this.addUIElement(this.danfangButton);
  }

  // 创建底部导航栏按钮
  createTabButton(x, y, width, height, text, index) {
    return new Button(
      this.ctx,
      x,
      y,
      width,
      height,
      text,
      null,
      null,
      () => {
        this.onTabSelected(index);
      }
    );
  }

  // 底部导航栏选中回调
  onTabSelected(index) {
    // 如果点击的是当前选中的项，不做处理
    if (this.selectedTabIndex === index) {
      return;
    }

    // 更新选中的索引
    this.selectedTabIndex = index;

    // 根据索引切换场景
    switch (index) {
      case 0:
        // 主页
        this.sceneManager.showScene('main');
        break;
      case 1:
        // 角色页面
        this.sceneManager.showScene('character');
        break;
      case 2:
        // 洞府页面，已经在洞府页面，不需要切换
        break;
      case 3:
        // 试炼页面
        this.sceneManager.showScene('trial');
        break;
      case 4:
        // 背包页面
        this.sceneManager.showScene('backpack');
        break;
    }
  }

  // 升级洞府
  upgradeDongfu() {
    // 获取玩家信息
    const player = game.gameStateManager.getPlayer();

    // 计算升级所需灵石
    const upgradeCost = this.getUpgradeCost(player.dongfuLevel);

    // 检查灵石是否足够
    if (player.resources.lingshi < upgradeCost) {
      console.log(`灵石不足，需要${upgradeCost}灵石`);
      return;
    }

    // 扣除灵石
    player.resources.lingshi -= upgradeCost;

    // 升级洞府
    player.dongfuLevel += 1;

    // 更新玩家信息
    game.gameStateManager.setPlayer(player);

    console.log(`洞府升级成功，当前等级：${player.dongfuLevel}`);
  }

  // 计算升级洞府所需灵石
  getUpgradeCost(currentLevel) {
    // 使用洞府系统的计算方法
    if (game.dongfuSystem) {
      return game.dongfuSystem.getUpgradeCost(currentLevel);
    }

    // 如果没有洞府系统，使用默认计算方法
    return currentLevel * 1000;
  }

  // 场景显示时的回调
  onShow(params) {
    // 清空UI元素
    this.clearUIElements();

    // 初始化UI（现在在onShow中调用，而不是构造函数中）
    this.initUI();

    // 更新选中的导航项
    this.selectedTabIndex = 2;
  }

  // 场景隐藏时的回调
  onHide() {
    // 清空UI元素
    this.clearUIElements();
  }

  // 子类实现的绘制逻辑
  drawScene() {
    // 绘制背景
    this.drawBackground();

    // 绘制顶部导航栏
    this.drawHeader();

    // 绘制洞府信息
    this.drawDongfuInfo();

    // 绘制底部导航栏
    this.drawTabBar();
  }

  // 绘制背景
  drawBackground() {
    // 如果有背景图资源，使用背景图
    if (this.resources && this.resources.dongfuBg) {
      try {
        this.ctx.drawImage(
          this.resources.dongfuBg,
          0,
          0,
          this.screenWidth,
          this.screenHeight
        );
      } catch (error) {
        // 如果绘制背景图失败，使用渐变色背景作为备选
        console.error('绘制洞府背景图片失败', error);
        this.drawGradientBackground();
      }
    } else {
      // 如果没有背景图资源，使用渐变色背景
      this.drawGradientBackground();
    }
  }

  // 绘制渐变色背景
  drawGradientBackground() {
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
    gradient.addColorStop(0, '#190a05');
    gradient.addColorStop(1, '#870000');

    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
  }

  // 绘制顶部导航栏
  drawHeader() {
    const headerHeight = 80;

    // 绘制顶部导航栏背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, 0, this.screenWidth, headerHeight);

    // 绘制头像框
    const avatarSize = 60;
    const avatarX = 10;
    const avatarY = 10;

    // 如果有头像资源，绘制头像
    if (this.resources && this.resources.avatarFrame) {
      this.ctx.drawImage(
        this.resources.avatarFrame,
        avatarX,
        avatarY,
        avatarSize,
        avatarSize
      );

      // 尝试获取玩家微信头像
      const avatarUrl = game.gameStateManager.getPlayer().avatarUrl;
      if (avatarUrl) {
        // 加载玩家头像
        const avatar = wx.createImage();
        avatar.onload = () => {
          // 绘制圆形头像
          this.ctx.save();
          this.ctx.beginPath();
          this.ctx.arc(avatarX + avatarSize / 2, avatarY + avatarSize / 2, avatarSize / 2 - 5, 0, Math.PI * 2);
          this.ctx.closePath();
          this.ctx.clip();

          this.ctx.drawImage(avatar, avatarX + 5, avatarY + 5, avatarSize - 10, avatarSize - 10);

          this.ctx.restore();
        };
        avatar.src = avatarUrl;
      } else {
        // 如果没有头像，绘制默认头像
        this.ctx.fillStyle = '#ffffff';
        this.ctx.beginPath();
        this.ctx.arc(avatarX + avatarSize / 2, avatarY + avatarSize / 2, avatarSize / 2 - 5, 0, Math.PI * 2);
        this.ctx.fill();
      }
    } else {
      // 如果没有头像框资源，绘制简单的圆形头像框
      this.ctx.strokeStyle = '#ffffff';
      this.ctx.lineWidth = 2;
      this.ctx.beginPath();
      this.ctx.arc(avatarX + avatarSize / 2, avatarY + avatarSize / 2, avatarSize / 2, 0, Math.PI * 2);
      this.ctx.stroke();
    }

    // 绘制玩家信息
    const player = game.gameStateManager.getPlayer();

    // 绘制玩家昵称
    this.ctx.font = '18px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(player.nickname, avatarX + avatarSize + 10, avatarY + 20);

    // 绘制洞府等级
    this.ctx.font = '16px Arial';
    this.ctx.fillText(`洞府等级: ${player.dongfuLevel}`, avatarX + avatarSize + 10, avatarY + 45);

    // 绘制资源信息
    const resourceY = headerHeight / 2;
    const iconSize = 20;

    // 绘制仙玉
    if (this.resources && this.resources.iconXianyu) {
      this.ctx.drawImage(
        this.resources.iconXianyu,
        this.screenWidth / 2,
        resourceY - iconSize / 2,
        iconSize,
        iconSize
      );
    } else {
      // 如果没有图标资源，绘制简单的圆形
      this.ctx.fillStyle = '#ffd700';
      this.ctx.beginPath();
      this.ctx.arc(this.screenWidth / 2 + iconSize / 2, resourceY, iconSize / 2, 0, Math.PI * 2);
      this.ctx.fill();
    }

    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`仙玉: ${player.resources.xianyu}`, this.screenWidth / 2 + iconSize + 5, resourceY + 5);

    // 绘制灵石
    const lingshiX = this.screenWidth / 2 + 120;

    if (this.resources && this.resources.iconLingshi) {
      this.ctx.drawImage(
        this.resources.iconLingshi,
        lingshiX,
        resourceY - iconSize / 2,
        iconSize,
        iconSize
      );
    } else {
      // 如果没有图标资源，绘制简单的方形
      this.ctx.fillStyle = '#c0c0c0';
      this.ctx.fillRect(lingshiX, resourceY - iconSize / 2, iconSize, iconSize);
    }

    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`灵石: ${player.resources.lingshi}`, lingshiX + iconSize + 5, resourceY + 5);
  }

  // 绘制洞府信息
  drawDongfuInfo() {
    const headerHeight = 80;
    const player = game.gameStateManager.getPlayer();

    // 获取洞府信息
    let dongfuName = '';
    let dongfuDescription = '';
    let lingqiAmount = 0;

    // 使用洞府系统获取信息
    if (game.dongfuSystem) {
      const dongfuInfo = game.dongfuSystem.getDongfuLevelInfo(player.dongfuLevel);
      dongfuName = dongfuInfo.name;
      dongfuDescription = dongfuInfo.description;
      lingqiAmount = dongfuInfo.lingqiAmount;
    } else {
      // 如果没有洞府系统，使用默认数据
      const dongfuNames = [
        '茅草屋',     // 1级
        '木屋',       // 2级
        '石屋',       // 3级
        '小院',       // 4级
        '大院',       // 5级
        '小洞府',     // 6级
        '中型洞府',   // 7级
        '大型洞府',   // 8级
        '灵气洞府',   // 9级
        '仙气洞府'    // 10级
      ];

      const descriptions = [
        '简陋的茅草屋，勉强能遮风挡雨。',
        '稍微结实的木屋，能安心休息了。',
        '坚固的石屋，不惧风雨侵袭。',
        '有了自己的小院子，可以种植一些灵草。',
        '宽敞的大院，开始聚集一些灵气。',
        '初具规模的洞府，有了稳定的灵气供应。',
        '洞府内灵气充沛，适合长期修炼。',
        '洞府已成规模，灵气浓度较高。',
        '洞府中灵气如云，修炼事半功倍。',
        '仙气缭绕的洞府，已接近仙家福地。'
      ];

      const index = Math.min(player.dongfuLevel - 1, dongfuNames.length - 1);
      dongfuName = dongfuNames[index];
      dongfuDescription = descriptions[index];
      lingqiAmount = 20 * Math.pow(2, player.dongfuLevel - 1); // 1级洞府20点灵气，每升一级翻倍
    }

    // 绘制洞府名称
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(`${dongfuName}`, this.screenWidth / 2, headerHeight + 40);

    // 绘制洞府等级
    this.ctx.font = '18px Arial';
    this.ctx.fillText(`洞府等级: ${player.dongfuLevel}`, this.screenWidth / 2, headerHeight + 70);

    // 绘制洞府灵气数量
    this.ctx.fillStyle = '#7FFFD4'; // 海蓝色
    this.ctx.fillText(`灵气数量: ${lingqiAmount}`, this.screenWidth / 2, headerHeight + 100);

    // 获取主角色信息
    const characters = game.gameStateManager.getCharacters();
    const mainCharacter = characters.find(char => char.id === 1);

    if (mainCharacter) {
      // 计算灵气吸收率
      let absorptionRate = 0.5; // 默认练气期吸收率为50%

      if (game.dongfuSystem) {
        absorptionRate = game.dongfuSystem.getCultivationAbsorptionRate(mainCharacter.cultivation);
      } else {
        // 如果没有洞府系统，使用默认计算方法
        if (mainCharacter.cultivation.includes('练气期')) {
          const layer = parseInt(mainCharacter.cultivation.replace(/[^0-9]/g, '')) || 1;
          absorptionRate = 0.5 + (layer - 1) * 0.01; // 每层增加1%
        } else if (mainCharacter.cultivation === '筑基期') {
          absorptionRate = 0.7;
        } else if (mainCharacter.cultivation === '金丹期') {
          absorptionRate = 0.75;
        } else if (mainCharacter.cultivation === '元婴期') {
          absorptionRate = 0.8;
        } else if (mainCharacter.cultivation === '化神期') {
          absorptionRate = 0.85;
        } else if (mainCharacter.cultivation === '练虚期') {
          absorptionRate = 0.9;
        } else if (mainCharacter.cultivation === '合体期') {
          absorptionRate = 0.95;
        } else if (mainCharacter.cultivation === '大乘期') {
          absorptionRate = 1.0;
        } else if (mainCharacter.cultivation === '渡劫期') {
          absorptionRate = 1.05;
        }
      }

      // 计算修炼速度
      const cultivationSpeed = Math.floor(lingqiAmount * absorptionRate);

      // 绘制修炼速度
      this.ctx.fillStyle = '#98FB98'; // 淡绿色
      this.ctx.fillText(`修炼速度: ${cultivationSpeed}/周期`, this.screenWidth / 2, headerHeight + 130);

      // 绘制灵气吸收率
      this.ctx.fillStyle = '#FFA07A'; // 淡橙色
      this.ctx.fillText(`灵气吸收率: ${(absorptionRate * 100).toFixed(0)}%`, this.screenWidth / 2, headerHeight + 160);
    }

    // 绘制升级所需灵石
    const upgradeCost = this.getUpgradeCost(player.dongfuLevel);
    this.ctx.fillStyle = player.resources.lingshi >= upgradeCost ? '#ffffff' : '#ff6666';
    this.ctx.fillText(`升级所需灵石: ${upgradeCost}`, this.screenWidth / 2, headerHeight + 190);

    // 绘制洞府描述
    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#cccccc';

    // 绘制多行文本
    const maxWidth = this.screenWidth - 60;
    const lineHeight = 24;

    this.wrapText(this.ctx, dongfuDescription, this.screenWidth / 2, headerHeight + 230, maxWidth, lineHeight);
  }

  // 多行文本绘制辅助函数
  wrapText(context, text, x, y, maxWidth, lineHeight) {
    const words = text.split(' ');
    let line = '';

    context.textAlign = 'center';

    for (let n = 0; n < words.length; n++) {
      const testLine = line + words[n] + ' ';
      const metrics = context.measureText(testLine);
      const testWidth = metrics.width;

      if (testWidth > maxWidth && n > 0) {
        context.fillText(line, x, y);
        line = words[n] + ' ';
        y += lineHeight;
      } else {
        line = testLine;
      }
    }

    context.fillText(line, x, y);
  }

  // 获取洞府效果
  getDongfuEffect(level) {
    return {
      lingqi: level * 5,  // 每级增加5%灵气
      speed: level * 3    // 每级增加3%修炼速度
    };
  }

  // 绘制底部导航栏
  drawTabBar() {
    const tabBarHeight = 60;
    const tabBarY = this.screenHeight - tabBarHeight;

    // 绘制底部导航栏背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, tabBarY, this.screenWidth, tabBarHeight);

    // 绘制分割线
    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
    this.ctx.lineWidth = 1;

    for (let i = 1; i < 5; i++) {
      const x = this.screenWidth * i / 5;
      this.ctx.beginPath();
      this.ctx.moveTo(x, tabBarY);
      this.ctx.lineTo(x, this.screenHeight);
      this.ctx.stroke();
    }

    // 绘制选中项的高亮背景
    const tabWidth = this.screenWidth / 5;
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
    this.ctx.fillRect(this.selectedTabIndex * tabWidth, tabBarY, tabWidth, tabBarHeight);

    // 导航栏文字
    const tabTexts = ['主页', '角色', '洞府', '试炼', '背包'];

    // 绘制导航栏图标和文字
    for (let i = 0; i < 5; i++) {
      const tabX = i * tabWidth + tabWidth / 2;

      // 绘制图标
      const iconSize = 30;
      const iconY = tabBarY + 10;

      // 如果有图标资源，绘制图标
      const iconKey = `tabIcon${i + 1}`;
      if (this.resources && this.resources[iconKey]) {
        this.ctx.drawImage(
          this.resources[iconKey],
          tabX - iconSize / 2,
          iconY,
          iconSize,
          iconSize
        );
      } else {
        // 如果没有图标资源，绘制简单的占位符
        this.ctx.fillStyle = i === this.selectedTabIndex ? '#ffffff' : '#a0a0a0';
        this.ctx.beginPath();
        this.ctx.arc(tabX, iconY + iconSize / 2, iconSize / 2, 0, Math.PI * 2);
        this.ctx.fill();
      }

      // 绘制文字
      this.ctx.font = '14px Arial';
      this.ctx.fillStyle = i === this.selectedTabIndex ? '#ffffff' : '#a0a0a0';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(tabTexts[i], tabX, tabBarY + 50);
    }
  }

  // 进入静室
  enterJingshi() {
    // 显示静室场景
    this.sceneManager.showScene('jingshi');
  }

  // 进入丹房
  enterDanfang() {
    // 显示丹房场景
    this.sceneManager.showScene('danfang');
  }
}

export default DongfuScene;