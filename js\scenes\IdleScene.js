/**
 * 挂机游历场景类
 * 获取灵石、一阶药材和功法修炼点
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import game from '../../game';

class IdleScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager, resources) {
    super(ctx, screenWidth, screenHeight, sceneManager);

    // 场景资源
    this.resources = resources || {};

    // 当前选中的底部导航项
    this.selectedTabIndex = 0; // 主页对应的导航索引

    // 返回按钮
    this.backButton = null;

    // 上次领取时间
    this.lastClaimTime = 0;

    // 基础每小时收益
    this.baseHourlyRewards = {
      lingshi: 100,    // 灵石产出
      herbs: 5,        // 一阶药材产出
      functionPoints: 2, // 功法修炼点产出
      beastMaterial1: 3, // 一阶兽材产出
      beastMaterial2: 0  // 二阶兽材产出，默认为0
    };

    // 收益加成系数（基于主线关卡进度）
    this.progressMultiplier = 0.1; // 每通过一章增加10%收益
  }

  // 获取玩家数据
  getPlayerData() {
    if (game.gameStateManager) {
      return game.gameStateManager.getPlayer() || {};
    }
    return {};
  }

  // 保存玩家数据
  savePlayerData() {
    if (game.gameStateManager) {
      game.gameStateManager.saveGameState();
    }
  }

  // 初始化UI
  initUI() {
    // 清空UI元素
    this.clearUIElements();

    // 创建底部导航栏按钮
    this.createTabButtons();

    // 创建返回按钮
    this.createBackButton();

    // 创建领取按钮
    this.createClaimButton();

    // 创建快速挂机按钮
    this.createQuickIdleButton();
  }

  // 创建底部导航栏按钮
  createTabButtons() {
    const tabBarHeight = 60;
    const tabBarY = this.screenHeight - tabBarHeight;
    const tabButtonWidth = this.screenWidth / 5;

    // 创建底部导航栏按钮
    this.tabButtons = [
      this.createTabButton(0 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '主页', 0),
      this.createTabButton(1 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '角色', 1),
      this.createTabButton(2 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '洞府', 2),
      this.createTabButton(3 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '试炼', 3),
      this.createTabButton(4 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '背包', 4)
    ];

    // 将按钮添加到UI元素列表
    this.tabButtons.forEach(button => {
      this.addUIElement(button);
    });
  }

  // 创建返回按钮
  createBackButton() {
    const buttonWidth = 80;
    const buttonHeight = 40;
    const margin = 10;

    this.backButton = new Button(
      this.ctx,
      margin,
      margin,
      buttonWidth,
      buttonHeight,
      '返回',
      null,
      null,
      () => {
        console.log('点击返回按钮');
        // 返回到主页面，携带from参数
        this.sceneManager.showScene('main', { from: 'idle' });
      }
    );

    this.addUIElement(this.backButton);
  }

  // 创建领取按钮
  createClaimButton() {
    const buttonWidth = 160;
    const buttonHeight = 60;

    // 计算可领取的奖励
    const rewards = this.calculateRewards();
    const canClaim = rewards.hasRewards;

    this.claimButton = new Button(
      this.ctx,
      (this.screenWidth - buttonWidth) / 2,
      this.screenHeight - 200,
      buttonWidth,
      buttonHeight,
      '领取奖励',
      null,
      canClaim ? null : 'rgba(100, 100, 100, 0.7)',
      () => {
        if (canClaim) {
          this.claimRewards();
        } else {
          wx.showToast({
            title: '暂无可领取的奖励',
            icon: 'none',
            duration: 2000
          });
        }
      }
    );

    this.addUIElement(this.claimButton);
  }

  // 创建快速挂机按钮
  createQuickIdleButton() {
    const buttonWidth = 160;
    const buttonHeight = 60;

    this.quickIdleButton = new Button(
      this.ctx,
      (this.screenWidth - buttonWidth) / 2,
      this.screenHeight - 120,
      buttonWidth,
      buttonHeight,
      '快速游历(10仙玉)',
      null,
      null,
      () => {
        this.confirmQuickIdle();
      }
    );

    this.addUIElement(this.quickIdleButton);
  }

  // 确认快速挂机
  confirmQuickIdle() {
    const playerData = this.getPlayerData();
    const resources = playerData.resources || {};
    const xianyu = resources.xianyu || 0;

    if (xianyu < 10) {
      wx.showToast({
        title: '仙玉不足',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    wx.showModal({
      title: '快速游历',
      content: '花费10仙玉获得2小时的游历收益，是否确认？',
      success: (res) => {
        if (res.confirm) {
          this.doQuickIdle();
        }
      }
    });
  }

  // 执行快速挂机
  doQuickIdle() {
    const playerData = this.getPlayerData();
    const resources = playerData.resources || {};

    // 扣除仙玉
    resources.xianyu = (resources.xianyu || 0) - 10;

    // 更新玩家数据
    game.gameStateManager.setPlayer({
      ...playerData,
      resources: resources
    });

    // 计算2小时的游历奖励
    const storyProgress = this.getStoryProgress();
    let multiplier = 1 + (storyProgress * this.progressMultiplier);

    // 获取VIP加成
    const vipBonus = this.getVIPBonus(playerData.vipLevel);
    if (vipBonus > 0) {
      multiplier += vipBonus;
    }

    // 获取玩家境界决定兽材掉落
    const mainCharacter = game.gameStateManager.getCharacterById(1);
    const playerRealm = mainCharacter ? mainCharacter.cultivation : '练气期一层';

    // 根据玩家境界决定兽材掉落
    let beastMaterial1 = this.baseHourlyRewards.beastMaterial1;
    let beastMaterial2 = this.baseHourlyRewards.beastMaterial2;

    // 如果玩家境界达到筑基期，有机会掉落二阶兽材
    if (playerRealm.includes('筑基期')) {
      beastMaterial2 = 1; // 每小时基础产出1个二阶兽材
    }

    const rewards = {
      lingshi: Math.floor(this.baseHourlyRewards.lingshi * 2 * multiplier),
      herbs: Math.floor(this.baseHourlyRewards.herbs * 2 * multiplier),
      functionPoints: Math.floor(this.baseHourlyRewards.functionPoints * 2 * multiplier),
      beastMaterial1: Math.floor(beastMaterial1 * 2 * multiplier),
      beastMaterial2: Math.floor(beastMaterial2 * 2 * multiplier)
    };

    // 增加灵石
    resources.lingshi = (resources.lingshi || 0) + rewards.lingshi;

    // 增加功法修炼点
    resources.functionPoints = (resources.functionPoints || 0) + rewards.functionPoints;

    // 添加一阶药材
    this.addHerbItem(rewards.herbs);

    // 添加兽材
    this.addBeastMaterialItem(1, rewards.beastMaterial1);
    this.addBeastMaterialItem(2, rewards.beastMaterial2);

    // 更新玩家数据
    game.gameStateManager.setPlayer({
      ...playerData,
      resources: resources
    });

    // 准备提示消息
    let toastMessage = `快速游历完成！获得${rewards.lingshi}灵石、${rewards.herbs}一阶药材、${rewards.beastMaterial1}一阶兽材`;

    // 如果有VIP加成，添加到提示中
    if (vipBonus > 0) {
      toastMessage += `\n包含VIP加成: +${(vipBonus * 100).toFixed(0)}%`;
    }

    // 显示领取成功消息
    wx.showToast({
      title: toastMessage,
      icon: 'success',
      duration: 2000
    });
  }

  // 创建底部导航栏按钮
  createTabButton(x, y, width, height, text, index) {
    return new Button(
      this.ctx,
      x,
      y,
      width,
      height,
      text,
      null,
      null,
      () => {
        this.onTabSelected(index);
      }
    );
  }

  // 底部导航栏选中回调
  onTabSelected(index) {
    // 如果点击的是当前选中的项，不做处理
    if (this.selectedTabIndex === index) {
      return;
    }

    // 更新选中的索引
    this.selectedTabIndex = index;

    // 根据索引切换场景
    switch (index) {
      case 0:
        // 主页
        this.sceneManager.showScene('main', { from: 'idle' });
        break;
      case 1:
        // 角色页面
        this.sceneManager.showScene('character');
        break;
      case 2:
        // 洞府页面
        this.sceneManager.showScene('dongfu');
        break;
      case 3:
        // 试炼页面
        this.sceneManager.showScene('trial');
        break;
      case 4:
        // 背包页面
        this.sceneManager.showScene('backpack');
        break;
    }
  }

  // 计算可领取的奖励
  calculateRewards() {
    const player = this.getPlayerData();

    // 获取上次领取时间
    const lastClaim = player.lastIdleClaim || 0;

    // 当前时间
    const now = Date.now();

    // 计算经过的小时数
    const hoursPassed = (now - lastClaim) / (60 * 60 * 1000);

    // 如果经过的时间不足15分钟，不提供奖励
    if (hoursPassed < 0.25) {
      return {
        hasRewards: false,
        rewards: {
          lingshi: 0,
          herbs: 0,
          functionPoints: 0,
          beastMaterial1: 0,
          beastMaterial2: 0
        },
        hoursPassed: hoursPassed
      };
    }

    // 获取主线进度
    const storyProgress = this.getStoryProgress();

    // 计算加成倍率
    let multiplier = 1 + (storyProgress * this.progressMultiplier);

    // 获取VIP加成
    const vipBonus = this.getVIPBonus(player.vipLevel);
    if (vipBonus > 0) {
      multiplier += vipBonus;
    }

    // 计算最终奖励（最多累积24小时）
    const cappedHours = Math.min(hoursPassed, 24);

    // 获取玩家境界决定兽材掉落
    const mainCharacter = game.gameStateManager.getCharacterById(1);
    const playerRealm = mainCharacter ? mainCharacter.cultivation : '练气期一层';

    // 根据玩家境界决定兽材掉落
    let beastMaterial1 = this.baseHourlyRewards.beastMaterial1;
    let beastMaterial2 = this.baseHourlyRewards.beastMaterial2;

    // 如果玩家境界达到筑基期，有机会掉落二阶兽材
    if (playerRealm.includes('筑基期')) {
      beastMaterial2 = 1; // 每小时基础产出1个二阶兽材
    }

    // 计算最终奖励
    const rewards = {
      lingshi: Math.floor(this.baseHourlyRewards.lingshi * cappedHours * multiplier),
      herbs: Math.floor(this.baseHourlyRewards.herbs * cappedHours * multiplier),
      functionPoints: Math.floor(this.baseHourlyRewards.functionPoints * cappedHours * multiplier),
      beastMaterial1: Math.floor(beastMaterial1 * cappedHours * multiplier),
      beastMaterial2: Math.floor(beastMaterial2 * cappedHours * multiplier)
    };

    return {
      hasRewards: rewards.lingshi > 0 || rewards.herbs > 0 || rewards.functionPoints > 0 || rewards.beastMaterial1 > 0 || rewards.beastMaterial2 > 0,
      rewards: rewards,
      hoursPassed: cappedHours,
      vipBonus: vipBonus > 0 ? vipBonus : 0
    };
  }

  // 获取VIP加成
  getVIPBonus(vipLevel) {
    if (!vipLevel || vipLevel <= 0) return 0;

    // 获取VIP系统
    if (!game.vipSystem) return 0;

    // 获取VIP等级信息
    const vipInfo = game.vipSystem.getVIPLevelInfo(vipLevel);
    if (!vipInfo || !vipInfo.benefits) return 0;

    // 返回VIP游历收益加成
    return vipInfo.benefits.idleRewardBonus || 0;
  }

  // 获取主线故事通关进度（已通关的章节数）
  getStoryProgress() {
    const playerData = this.getPlayerData();
    let maxChapter = 0;

    // 检查每个章节的进度
    for (let chapterId = 1; chapterId <= 10; chapterId++) {
      const chapterKey = `chapter_${chapterId}`;
      const chapterProgress = playerData[chapterKey] || 0;

      // 如果该章节有进度，则更新最大已通关章节
      if (chapterProgress > 0) {
        maxChapter = chapterId;
      }
    }

    return maxChapter;
  }

  // 领取奖励
  claimRewards() {
    const player = this.getPlayerData();
    const rewardsData = this.calculateRewards();

    if (!rewardsData.hasRewards) {
      wx.showToast({
        title: '暂无可领取的奖励',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 获取当前资源
    const resources = player.resources || {};

    // 更新灵石
    resources.lingshi = (resources.lingshi || 0) + rewardsData.rewards.lingshi;

    // 更新功法修炼点
    resources.functionPoints = (resources.functionPoints || 0) + rewardsData.rewards.functionPoints;

    // 更新上次领取时间
    player.lastIdleClaim = Date.now();

    // 添加一阶药材
    this.addHerbItem(rewardsData.rewards.herbs);

    // 添加兽材
    this.addBeastMaterialItem(1, rewardsData.rewards.beastMaterial1);
    this.addBeastMaterialItem(2, rewardsData.rewards.beastMaterial2);

    // 更新玩家数据
    game.gameStateManager.setPlayer({
      ...player,
      resources: resources
    });

    // 显示领取成功消息
    wx.showToast({
      title: `游历奖励领取成功！获得${rewardsData.rewards.lingshi}灵石、${rewardsData.rewards.herbs}一阶药材、${rewardsData.rewards.beastMaterial1}一阶兽材`,
      icon: 'success',
      duration: 2000
    });

    // 重新创建领取按钮
    this.createClaimButton();
  }

  // 添加一阶药材到背包
  addHerbItem(count) {
    if (!count || count <= 0) return;

    const items = game.gameStateManager.getItems();

    // 检查是否已有一阶药材
    const existingHerb = items.find(item => item.name === '一阶药材');

    if (existingHerb) {
      // 已有药材，增加数量
      existingHerb.count += count;
      game.gameStateManager.updateItem(existingHerb.id, existingHerb);
    } else {
      // 没有药材，创建新药材
      const newHerb = {
        id: Date.now(), // 生成唯一ID
        name: '一阶药材',
        type: 'material',
        quality: 1,
        count: count,
        description: '初级炼丹材料，可用于炼制一阶丹药'
      };

      game.gameStateManager.addItem(newHerb);
    }
  }

  // 添加兽材到背包
  addBeastMaterialItem(tier, count) {
    if (!count || count <= 0) return;

    const items = game.gameStateManager.getItems();
    const itemName = tier === 1 ? '一阶兽材' : '二阶兽材';
    const itemQuality = tier === 1 ? 1 : 2;
    const itemDescription = tier === 1 ?
      '初级锻造材料，可用于锻造一阶装备' :
      '中级锻造材料，可用于锻造二阶装备，需要筑基期以上境界才能使用';

    // 检查是否已有兽材
    const existingMaterial = items.find(item => item.name === itemName);

    if (existingMaterial) {
      // 已有兽材，增加数量
      existingMaterial.count += count;
      game.gameStateManager.updateItem(existingMaterial.id, existingMaterial);
    } else {
      // 没有兽材，创建新兽材
      const newMaterial = {
        id: `beast_material_${tier}_${Date.now()}`, // 生成唯一ID
        name: itemName,
        type: 'material',
        quality: itemQuality,
        count: count,
        tier: tier,
        description: itemDescription
      };

      game.gameStateManager.addItem(newMaterial);
    }
  }

  // 场景显示回调
  onShow(params) {
    // 清空UI元素
    this.clearUIElements();

    // 初始化UI
    this.initUI();

    // 更新选中的导航项
    this.selectedTabIndex = 0;
  }

  // 绘制场景
  drawScene() {
    // 绘制背景
    this.drawBackground();

    // 绘制顶部导航栏
    this.drawHeader();

    // 绘制闲置修炼信息
    this.drawIdleInfo();

    // 绘制底部导航栏
    this.drawTabBar();
  }

  // 绘制背景
  drawBackground() {
    // 使用渐变色背景
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
    gradient.addColorStop(0, '#4a148c');
    gradient.addColorStop(1, '#000000');

    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
  }

  // 绘制顶部导航栏
  drawHeader() {
    const headerHeight = 60;

    // 绘制标题栏背景
    this.ctx.fillStyle = '#333366';
    this.ctx.fillRect(0, 0, this.screenWidth, headerHeight);

    // 绘制标题文本
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.font = 'bold 20px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText('挂机游历', this.screenWidth / 2, headerHeight / 2);
  }

  // 绘制闲置修炼信息
  drawIdleInfo() {
    const rewards = this.calculateRewards();

    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.font = 'bold 24px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('挂机游历', this.screenWidth / 2, 120);

    // 绘制已挂机时间
    this.ctx.font = '18px Arial';
    const hourText = rewards.hoursPassed.toFixed(1);
    this.ctx.fillText(
      `已游历时间: ${hourText}小时`,
      this.screenWidth / 2,
      160
    );

    // 计算每小时收益
    const player = this.getPlayerData();
    let multiplier = 1 + (this.getStoryProgress() * this.progressMultiplier);

    // 获取VIP加成
    const vipBonus = this.getVIPBonus(player.vipLevel);
    if (vipBonus > 0) {
      multiplier += vipBonus;
    }

    // 获取玩家境界决定兽材掉落
    const mainCharacter = game.gameStateManager.getCharacterById(1);
    const playerRealm = mainCharacter ? mainCharacter.cultivation : '练气期一层';

    // 根据玩家境界决定兽材掉落
    let beastMaterial1 = this.baseHourlyRewards.beastMaterial1;
    let beastMaterial2 = this.baseHourlyRewards.beastMaterial2;

    // 如果玩家境界达到筑基期，有机会掉落二阶兽材
    if (playerRealm.includes('筑基期')) {
      beastMaterial2 = 1; // 每小时基础产出1个二阶兽材
    }

    const hourlyRewards = {
      lingshi: Math.floor(this.baseHourlyRewards.lingshi * multiplier),
      herbs: Math.floor(this.baseHourlyRewards.herbs * multiplier),
      functionPoints: Math.floor(this.baseHourlyRewards.functionPoints * multiplier),
      beastMaterial1: Math.floor(beastMaterial1 * multiplier),
      beastMaterial2: Math.floor(beastMaterial2 * multiplier)
    };

    // 绘制每小时收益
    this.ctx.fillStyle = '#FFCC00'; // 黄色
    this.ctx.font = 'bold 18px Arial';
    this.ctx.fillText(
      `每小时收益:`,
      this.screenWidth / 2,
      190
    );

    this.ctx.font = '16px Arial';
    this.ctx.fillText(
      `灵石: ${hourlyRewards.lingshi} | 一阶药材: ${hourlyRewards.herbs} | 兽材: ${hourlyRewards.beastMaterial1}`,
      this.screenWidth / 2,
      215
    );

    this.ctx.fillText(
      `功法点: ${hourlyRewards.functionPoints}${hourlyRewards.beastMaterial2 > 0 ? ` | 二阶兽材: ${hourlyRewards.beastMaterial2}` : ''}`,
      this.screenWidth / 2,
      240
    );
    this.ctx.fillStyle = '#FFFFFF'; // 恢复白色

    // 绘制奖励信息
    this.ctx.font = '18px Arial';
    this.ctx.fillText(
      `当前可获得:`,
      this.screenWidth / 2,
      265
    );

    this.ctx.fillText(
      `灵石: ${rewards.rewards.lingshi}`,
      this.screenWidth / 2,
      290
    );

    this.ctx.fillText(
      `一阶药材: ${rewards.rewards.herbs}`,
      this.screenWidth / 2,
      315
    );

    this.ctx.fillText(
      `一阶兽材: ${rewards.rewards.beastMaterial1}`,
      this.screenWidth / 2,
      340
    );

    if (rewards.rewards.beastMaterial2 > 0) {
      this.ctx.fillText(
        `二阶兽材: ${rewards.rewards.beastMaterial2}`,
        this.screenWidth / 2,
        365
      );
    }

    this.ctx.fillText(
      `功法修炼点: ${rewards.rewards.functionPoints}`,
      this.screenWidth / 2,
      rewards.rewards.beastMaterial2 > 0 ? 390 : 365
    );

    // 绘制VIP加成信息
    if (rewards.vipBonus && rewards.vipBonus > 0) {
      this.ctx.fillStyle = '#FFD700'; // 金色
      this.ctx.fillText(
        `VIP加成: +${(rewards.vipBonus * 100).toFixed(0)}%`,
        this.screenWidth / 2,
        rewards.rewards.beastMaterial2 > 0 ? 415 : 390
      );
      this.ctx.fillStyle = '#FFFFFF'; // 恢复白色
    }

    // 绘制说明文字
    this.ctx.font = '16px Arial';
    this.ctx.fillText(
      '离线时也会累积游历奖励',
      this.screenWidth / 2,
      rewards.rewards.beastMaterial2 > 0 ? 440 : 415
    );

    this.ctx.fillText(
      '最多累积24小时的奖励',
      this.screenWidth / 2,
      rewards.rewards.beastMaterial2 > 0 ? 465 : 440
    );
  }

  // 绘制底部导航栏
  drawTabBar() {
    const tabBarHeight = 60;
    const tabBarY = this.screenHeight - tabBarHeight;

    // 绘制底部导航栏背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    this.ctx.fillRect(0, tabBarY, this.screenWidth, tabBarHeight);

    // 绘制底部导航栏按钮
    this.tabButtons.forEach((button, index) => {
      // 选中状态使用不同的颜色
      if (index === this.selectedTabIndex) {
        button.backgroundColor = 'rgba(255, 255, 255, 0.2)';
      } else {
        button.backgroundColor = null;
      }
    });
  }
}

// 使用CommonJS格式导出，确保与项目导入方式兼容
module.exports = IdleScene;